/*
 评论页面需求说明（2024-07-10）

 1. 仅展示一级评论（不自动展示二级评论列表）。
 2. 一级评论模型字段 isHasChild==true 时，底部展示"展开N条回复"按钮（N=childCount），样式如"展开99条回复↓"。
 3. 点击"展开N条回复"时，异步拉取2条二级评论，拼接到当前一级评论下方，继续点击则每次再拉2条，直到全部加载完毕。
 4. 一级评论支持图片展示：commentImg 字段有值时，展示一张图片（用 Kingfisher 加载）。
 5. 二级评论同样支持图片展示（如有 commentImg 字段）。
 6. 点赞按钮右侧新增"不喜欢"按钮，UI与点赞按钮一致，点击后触发相应回调。
 7. 不展示二级评论的独立页面，所有二级评论都在一级评论下方动态拼接。
 8. 一级评论和二级评论都需支持点赞/不喜欢操作。
 9. 一级评论和二级评论都需支持图片展示（如有）。
 10. 作者标签：若评论用户为视频作者，用户名右侧展示"作者"标签。
 11. @功能：若pcommentUserVo存在，用户名后展示"@被回复人昵称"，样式需区分。
 12. 其他UI细节严格参考产品设计图。
*/
import UIKit
import IQTextView
import IQKeyboardManagerSwift
import PhotosUI
import Kingfisher // 新增

class VideoCommentViewController: UIViewController, UITableViewDelegate, UITableViewDataSource, UIGestureRecognizerDelegate, UITextViewDelegate, PHPickerViewControllerDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    // MARK: - Properties
    private var videoId: Int = 0   // 作品 ID，用于拉取评论

    private var commentCount: Int = 0 // 初始评论总数，由视频模型提供

    // 视频相关数据，用于中间层显示
    private var videoLikeCount: Int = 0
    private var videoCollectCount: Int = 0
    private var isVideoLiked: Bool = false
    private var isVideoCollected: Bool = false

    private var comments: [CommentModel] = []
    private var isSending = false // 发送中标记
    private var pageSize = 20
    private var lastCommentId: Int? = nil
    private var lastCreateTime: String? = nil
    private var isLoading = false
    private var hasMoreData = true

    // 底部提示
    private let footerLabel: UILabel = {
        let label = UILabel()
        label.text = "到底了~"
        label.textAlignment = .center
        label.font = .systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        label.frame = CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 44)
        label.isHidden = true
        return label
    }()
    
    // 新增：二级评论 Cell 标识
    private let replyCellIdentifier = "CommentReplyCell"
    
    // 新增：背景遮罩视图
    private var backgroundView: UIView!
    
    // MARK: - Row Mapping
    private enum RowType { case comment, reply, expand }

    /// 生成显示行映射：[(RowType, indexInComments)]
    private func makeRowMap() -> [(RowType, Int)] {
        var rows: [(RowType, Int)] = []
        var idx = 0
        while idx < comments.count {
            let model = comments[idx]
            if model.level == 0 {
                rows.append((.comment, idx))
                var next = idx + 1
                while next < comments.count, comments[next].level == 1 {
                    rows.append((.reply, next))
                    next += 1
                }
                if model.isHasChild && model.showExpandReplies {
                    rows.append((.expand, idx)) // parent idx reference
                }
                idx = next
            } else {
                rows.append((.reply, idx))
                idx += 1
            }
        }
        return rows
    }

    private var cachedRowMap: [(RowType, Int)] { makeRowMap() }
    
    // 数据同步回调
    var onVideoDataChanged: ((Int, Int, Bool, Bool) -> Void)?

    // MARK: - Initializer
    init(videoId: Int = 0, initialCount: Int = 0, likeCount: Int = 0, collectCount: Int = 0, isLiked: Bool = false, isCollected: Bool = false) {
        self.videoId = videoId
        self.commentCount = initialCount
        self.videoLikeCount = likeCount
        self.videoCollectCount = collectCount
        self.isVideoLiked = isLiked
        self.isVideoCollected = isCollected
        super.init(nibName: nil, bundle: nil)
    }

    @available(*, unavailable)
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        view.layer.cornerRadius = 24
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.clipsToBounds = true
        return view
    }()
    
    private lazy var headerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "评论 \(commentCount)"
        label.font = .boldSystemFont(ofSize: 18)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = UIColor(hex: "#F5F5F5")
        table.separatorStyle = .none
        table.register(CommentCell.self, forCellReuseIdentifier: "CommentCell")
        table.register(CommentReplyCell.self, forCellReuseIdentifier: replyCellIdentifier)
        table.register(ExpandRepliesCell.self, forCellReuseIdentifier: "ExpandRepliesCell")
        table.estimatedRowHeight = 140
        table.rowHeight = UITableView.automaticDimension
        return table
    }()
    
    // MARK: - Pull Indicator
    private lazy var pullIndicator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#DDDDDD")
        view.layer.cornerRadius = 2
        return view
    }()
    
    // MARK: - 评论输入区（新版）
    private lazy var commentInputBar: UIView = {
        let view = UIView()
//        view.backgroundColor =
        view.backgroundColor = .white
//        view.layer.cornerRadius = 12
        view.layer.masksToBounds = true

        // 输入框
        let inputField = IQTextView()
        inputField.backgroundColor = UIColor(hex: "#000000", alpha: 0.1)
        inputField.textColor = UIColor(hex: "#000000")
        inputField.layer.cornerRadius = 12
        inputField.font = .systemFont(ofSize: 14)
        inputField.placeholder = "说点什么..."
        inputField.placeholderTextColor = UIColor(hex: "#000000", alpha: 0.45)
        inputField.returnKeyType = .send
        inputField.textContainerInset = UIEdgeInsets(top: 9, left: 10, bottom: 9, right: 10)
        inputField.delegate = self

        // 设置默认的输入属性，确保文本样式一致
        inputField.typingAttributes = [
            .font: UIFont.systemFont(ofSize: 14),
            .foregroundColor: UIColor.black
        ]

        view.addSubview(inputField)
        inputFieldRef = inputField
        // 添加监听输入框编辑开始事件
        // inputField.addTarget(self, action: #selector(inputFieldDidBeginEditing), for: .editingDidBegin)
        inputField.snp.makeConstraints { make in
            make.top.equalTo(view).offset(9)
            make.left.equalTo(view).offset(18)
            make.right.equalTo(view).offset(-18)
            make.height.equalTo(72)
        }

        // @按钮
        let atButton = UIButton(type: .system)
        atButton.setImage(UIImage(named: "icon_input_AtButton"), for: .normal)
        atButton.tintColor = .gray
        view.addSubview(atButton)
        atButton.addTarget(self, action: #selector(atButtonTapped), for: .touchUpInside)
        atButton.snp.makeConstraints { make in
            make.top.equalTo(inputField.snp.bottom).offset(9)
            make.left.equalTo(inputField)
            make.width.height.equalTo(24)
        }

        // 图片按钮
        let imageButton = UIButton(type: .system)
        imageButton.setImage(UIImage(named: "icon_input_ImageButton"), for: .normal)
        imageButton.tintColor = .gray
        view.addSubview(imageButton)
        imageButton.snp.makeConstraints { make in
            make.top.equalTo(atButton)
            make.left.equalTo(atButton.snp.right).offset(15)
            make.width.height.equalTo(24)
        }
        imageButton.addTarget(self, action: #selector(imageButtonTapped), for: .touchUpInside)

        // 表情按钮
        let emojiButton = UIButton(type: .system)
        emojiButton.setImage(UIImage(named: "icon_input_EmotionButton"), for: .normal)
        emojiButton.tintColor = .gray
        view.addSubview(emojiButton)
        emojiButton.snp.makeConstraints { make in
            make.top.equalTo(atButton)
            make.left.equalTo(imageButton.snp.right).offset(15)
            make.width.height.equalTo(24)
        }
        emojiButton.addTarget(self, action: #selector(emojiButtonTapped), for: .touchUpInside)

        // 发送按钮
        let sendButton = UIButton(type: .system)
        sendButton.setTitle("发送", for: .normal)
        sendButton.setTitleColor(.white, for: .normal)
        sendButton.backgroundColor = UIColor(hex: "#FF8F1F", alpha: 0.5)
        sendButton.layer.cornerRadius = 15.5
        sendButton.layer.masksToBounds = true
        sendButton.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        sendButton.isEnabled = false // 初始状态为禁用
        view.addSubview(sendButton)
        sendButtonRef = sendButton
        sendButton.snp.makeConstraints { make in
            make.top.equalTo(inputField.snp.bottom).offset(9)
            make.right.equalTo(inputField)
            make.width.equalTo(55)
            make.height.equalTo(31)
        }
        sendButton.addTarget(self, action: #selector(sendButtonTapped), for: .touchUpInside)

        // 图片预览容器（默认隐藏）
        let previewContainer = UIView()
        previewContainer.isHidden = true
        previewContainer.layer.cornerRadius = 4
        previewContainer.clipsToBounds = true
        view.addSubview(previewContainer)
        previewContainer.snp.makeConstraints { make in
            make.top.equalTo(atButton.snp.bottom).offset(16)
            make.left.equalTo(atButton)
            make.width.height.equalTo(40)
        }
        self.imagePreviewContainer = previewContainer

        let previewImageView = UIImageView()
        previewImageView.contentMode = .scaleAspectFill
        previewImageView.clipsToBounds = true
        previewContainer.addSubview(previewImageView)
        previewImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        self.imagePreviewImageView = previewImageView

        // 关闭预览按钮
        let closeBtn = UIButton(type: .custom)
        // 使用指定的清除图标资源
        closeBtn.setImage(UIImage(named: "comment_claer"), for: .normal)
        closeBtn.isHidden = true
        closeBtn.addTarget(self, action: #selector(removeImagePreview), for: .touchUpInside)
        view.addSubview(closeBtn)
        closeBtn.snp.makeConstraints { make in
            make.width.height.equalTo(14)
            make.centerY.equalTo(previewContainer.snp.top)
            make.centerX.equalTo(previewContainer.snp.right)
        }
        self.imagePreviewCloseButton = closeBtn

        return view
    }()

    // MARK: - 中间层装饰输入框
    private var decorativeLikeCountLabel: UILabel!
    private var decorativeCollectCountLabel: UILabel!
    private var decorativeLikeIcon: UIImageView!
    private var decorativeCollectIcon: UIImageView!

    private lazy var decorativeInputBar: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.isHidden = false // 默认显示

        // 装饰输入框
        let inputContainer = UIView()
        inputContainer.backgroundColor = UIColor(hex: "#EDEDED")
        inputContainer.layer.cornerRadius = 17.5
        view.addSubview(inputContainer)

        // 编辑图标
        let editIcon = UIImageView()
        editIcon.image = UIImage(named: "video_comment_edit")
        editIcon.contentMode = .scaleAspectFit
        inputContainer.addSubview(editIcon)
        editIcon.snp.makeConstraints { make in
            make.left.equalTo(inputContainer).offset(12)
            make.centerY.equalTo(inputContainer)
            make.width.height.equalTo(15)
        }

        // 占位文字
        let placeholderLabel = UILabel()
        placeholderLabel.text = "说点什么吧..."
        placeholderLabel.font = .systemFont(ofSize: 13)
        placeholderLabel.textColor = UIColor(hex: "#777777")
        inputContainer.addSubview(placeholderLabel)
        placeholderLabel.snp.makeConstraints { make in
            make.left.equalTo(editIcon.snp.right).offset(8)
            make.centerY.equalTo(inputContainer)
        }

        // 收藏数量（最右边）
        let collectCountLabel = UILabel()
        collectCountLabel.text = "\(videoCollectCount)"
        collectCountLabel.font = .systemFont(ofSize: 13)
        collectCountLabel.textColor = UIColor(hex: "#777777")
        view.addSubview(collectCountLabel)
        collectCountLabel.snp.makeConstraints { make in
            make.right.equalTo(view).offset(-12)
            make.centerY.equalTo(inputContainer)
        }
        self.decorativeCollectCountLabel = collectCountLabel

        // 收藏图标（收藏数量左边）
        let collectIcon = UIImageView()
        collectIcon.image = UIImage(named: isVideoCollected ? "video_collect_selected" : "video_collect")
        collectIcon.contentMode = .scaleAspectFit
        collectIcon.isUserInteractionEnabled = true
        view.addSubview(collectIcon)
        collectIcon.snp.makeConstraints { make in
            make.right.equalTo(collectCountLabel.snp.left).offset(-4)
            make.centerY.equalTo(inputContainer)
            make.width.height.equalTo(23)
        }
        self.decorativeCollectIcon = collectIcon

        // 添加收藏点击手势
        let collectGesture = UITapGestureRecognizer(target: self, action: #selector(decorativeCollectTapped))
        collectIcon.addGestureRecognizer(collectGesture)

        // 点赞数量（收藏图标左边）
        let likeCountLabel = UILabel()
        likeCountLabel.text = "\(videoLikeCount)"
        likeCountLabel.font = .systemFont(ofSize: 13)
        likeCountLabel.textColor = UIColor(hex: "#777777")
        view.addSubview(likeCountLabel)
        likeCountLabel.snp.makeConstraints { make in
            make.right.equalTo(collectIcon.snp.left).offset(-8)
            make.centerY.equalTo(inputContainer)
        }
        self.decorativeLikeCountLabel = likeCountLabel

        // 点赞图标（点赞数量左边）
        let likeIcon = UIImageView()
        likeIcon.image = UIImage(named: isVideoLiked ? "video_like_selected" : "video_like")
        likeIcon.contentMode = .scaleAspectFit
        likeIcon.isUserInteractionEnabled = true
        view.addSubview(likeIcon)
        likeIcon.snp.makeConstraints { make in
            make.right.equalTo(likeCountLabel.snp.left).offset(-4)
            make.centerY.equalTo(inputContainer)
            make.width.height.equalTo(23)
        }
        self.decorativeLikeIcon = likeIcon

        // 添加点赞点击手势
        let likeGesture = UITapGestureRecognizer(target: self, action: #selector(decorativeLikeTapped))
        likeIcon.addGestureRecognizer(likeGesture)

        // 装饰输入框约束
        inputContainer.snp.makeConstraints { make in
            make.left.equalTo(view).offset(12)
            make.top.equalTo(view).offset(15)
            make.right.equalTo(likeIcon.snp.left).offset(-10)
            make.height.equalTo(35)
        }

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(decorativeInputTapped))
        inputContainer.addGestureRecognizer(tapGesture)
        inputContainer.isUserInteractionEnabled = true

        return view
    }()

    // 替换原有commentInputView相关布局为新版commentInputBar
    private var commentInputBarBottomConstraint: NSLayoutConstraint?
    private var commentInputBarHeightConstraint: NSLayoutConstraint?
    private var decorativeInputBarBottomConstraint: NSLayoutConstraint?
    private var decorativeInputBarHeightConstraint: NSLayoutConstraint?
    
    // MARK: - EmojiKeyboardView
    class EmojiKeyboardView: UIView, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
        var emojiSelected: ((String) -> Void)?
        private let emojis = ["😀","😁","😂","🤣","😃","😄","😅","😆","😉","😊","😋","😎","😍","😘","🥰","😗","😙","😚","🙂","🤗","🤩","🤔","🤨","😐","😑","😶","🙄","😏","😣","😥","😮","🤐","😯","😪","😫","😴","😌","😛","😜","😝","🤤","😒","😓","😔","😕","🙃","🤑","😲","☹️","🙁","😖","😞","😟","😤","😢","😭","😦","😧","😨","😩","🤯","😬","😰","😱","🥵","🥶","😳","🤪","😵","😡","😠","🤬","😷","🤒","🤕","🤢","🤮","🤧","😇","🥳","🥺","🤠","🤡","🤥","🤫","🤭","🧐","🤓"]
        private let collectionView: UICollectionView
        
        override init(frame: CGRect) {
            let layout = UICollectionViewFlowLayout()
            let itemWidth = UIScreen.main.bounds.width / 8
            layout.itemSize = CGSize(width: itemWidth, height: 44)
            layout.minimumInteritemSpacing = 0
            layout.minimumLineSpacing = 0
            layout.sectionInset = .zero
            collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
            super.init(frame: frame)
            collectionView.dataSource = self
            collectionView.delegate = self
            collectionView.register(UICollectionViewCell.self, forCellWithReuseIdentifier: "EmojiCell")
            collectionView.backgroundColor = .clear
            addSubview(collectionView)
            collectionView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                collectionView.topAnchor.constraint(equalTo: topAnchor),
                collectionView.bottomAnchor.constraint(equalTo: bottomAnchor),
                collectionView.leadingAnchor.constraint(equalTo: leadingAnchor),
                collectionView.trailingAnchor.constraint(equalTo: trailingAnchor)
            ])
        }
        required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
        
        func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int { emojis.count }
        func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "EmojiCell", for: indexPath)
            let label = UILabel()
            label.text = emojis[indexPath.item]
            label.font = .systemFont(ofSize: 28)
            label.textAlignment = .center
            cell.contentView.subviews.forEach { $0.removeFromSuperview() }
            cell.contentView.addSubview(label)
            label.frame = cell.contentView.bounds
            return cell
        }
        func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
            emojiSelected?(emojis[indexPath.item])
        }
    }

    // VideoCommentViewController 内部属性
    private var emojiKeyboardView: EmojiKeyboardView?
    private var lastKeyboardHeight: CGFloat = 291
    private var isEmojiKeyboardVisible = false
    private var inputFieldRef: IQTextView? = nil
    private var sendButtonRef: UIButton? = nil
    private var isSwitchingToEmojiKeyboard = false

    // MARK: - Image Preview Support
    private var selectedImage: UIImage? = nil // 选中的待发送图片
    private var imagePreviewContainer: UIView? = nil
    private var imagePreviewImageView: UIImageView? = nil
    private var imagePreviewCloseButton: UIButton? = nil
    private let previewExtraHeight: CGFloat = 56 // 40 + 16

    // MARK: - Reply Target Tracking
    private let defaultPlaceholder = "说点什么..."
    private var replyTargetUsername: String? = nil {
        didSet {
            if let name = replyTargetUsername, !name.isEmpty {
                inputFieldRef?.placeholder = "回复 @\(name)"
            } else {
                inputFieldRef?.placeholder = defaultPlaceholder
            }
        }
    }
    private var replyPid: Int? = nil
    private var replyPcustomerId: String? = nil
    private var selectedParentIndex: Int?
    private var clickedIndex: Int?
    
    // MARK: - @ 提及搜索面板相关
    private var atPanelView: UIView?
    private var atPanelMaskView: UIView? // @面板上方的蒙版
    private var inputMaskView: UIView? // 输入框上方的蒙版（用于正常输入时）
    private var atPanelBottomConstraint: NSLayoutConstraint? // @面板底部约束，跟随输入框
    private var atPanelMaskBottomConstraint: NSLayoutConstraint? // @面板蒙版底部约束，跟随@面板
    private var mentionCollectionView: UICollectionView?
    private var mentionUsersData: [UserSearchResultsItem] = []
    private var mentionKeyword: String = ""
    private var mentionPage: Int = 0
    private var mentionHasMore: Bool = true
    private var mentionTotal: Int = 0
    private let mentionPageSize: Int = 10
    private let mentionPlaceholder: String = "输入搜索@的人"
    private var debounceWorkItem: DispatchWorkItem?
    private var atSearchWorkItem: DispatchWorkItem? // @搜索防抖
    private var isMentioning: Bool = false // 是否在@提及状态
    private var isAtListening: Bool = false // 是否在监听@输入
    private var isAtPanelVisible: Bool = false // @面板是否可见
    private var mentionStartLocation: Int = 0
    private var mentionedUserDict: [String: String] = [:]

    // @推荐用户缓存
    private var cachedRecommendUsers: [UserSearchResultsItem] = []
    private var recommendCacheTime: Date?
    
    // MARK: - 子评论分页追踪
    // 二级评论游标记录：key 为父评论 id，value 为(最后一条回复 id, 最后一条回复 createTime 字符串, 已加载数量)
    private var replyCursorTracker: [String: (lastId: Int?, lastTime: String?, loadedCount: Int)] = [:]
    
    // MARK: - 输入面板状态管理
    private enum InputPanelState {
        case none           // 全部收起
        case keyboard       // 文字键盘弹出
        case emoji          // 表情盘弹出
        case atPanel        // 艾特面板弹出（预留）
    }

    private var inputPanelState: InputPanelState = .none {
        didSet {
            // 仅在状态真正变化时处理，避免后台→前台产生大量重复日志
            guard oldValue != inputPanelState else { return }
            print("[VideoComment] inputPanelState changed from \(oldValue) to \(inputPanelState)")
            updateInputPanelState(from: oldValue, to: inputPanelState)
            updateIQKeyboardManagerSettings()
            updateInputMaskVisibility()
        }
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        // 只有在初始化时传入有效videoId才加载评论
        if videoId > 0 {
            loadComments()
        }
        setupKeyboardObservers()

        // 设置tableView的调试边框
        tableView.layer.borderWidth = 0
        tableView.layer.borderColor = UIColor.red.cgColor

        // 初始化发送按钮状态
        updateSendButtonState()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 完全禁用IQKeyboardManager，使用自定义键盘管理
        disableIQKeyboardManager()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 恢复IQKeyboardManager设置
        enableIQKeyboardManager()
    }

    // MARK: - IQKeyboardManager 完全控制
    private func disableIQKeyboardManager() {
        IQKeyboardManager.shared.isEnabled = false
        IQKeyboardManager.shared.resignOnTouchOutside = false
        IQKeyboardManager.shared.toolbarConfiguration.useTextInputViewTintColor = false
        IQKeyboardManager.shared.playInputClicks = false
        print("[VideoComment] 完全禁用IQKeyboardManager")
    }

    private func enableIQKeyboardManager() {
        IQKeyboardManager.shared.isEnabled = true
        IQKeyboardManager.shared.resignOnTouchOutside = true
        print("[VideoComment] 恢复IQKeyboardManager")
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 动态修正输入bar高度，保证safeAreaBottom正确
        switch inputPanelState {
        case .none:
            let baseHeight: CGFloat = 132 + (selectedImage != nil ? previewExtraHeight : 0)
            commentInputBarHeightConstraint?.constant = baseHeight + WindowUtil.safeAreaBottom
        default:
            let baseHeight: CGFloat = 132 + (selectedImage != nil ? previewExtraHeight : 0)
            commentInputBarHeightConstraint?.constant = baseHeight
        }
        // 立即刷新布局
        view.layoutIfNeeded()
    }

    // MARK: - IQKeyboardManager 管理
    private func updateIQKeyboardManagerSettings() {
        switch inputPanelState {
        case .atPanel:
            // @面板状态：完全禁用IQKeyboardManager的所有自动行为
            IQKeyboardManager.shared.isEnabled = false
            IQKeyboardManager.shared.resignOnTouchOutside = false
            print("[VideoComment] @面板状态：完全禁用IQKeyboardManager")
        default:
            // 其他状态：保持基本禁用，但允许部分功能
            IQKeyboardManager.shared.isEnabled = false
            IQKeyboardManager.shared.resignOnTouchOutside = false
        }
    }

    // MARK: - 输入框蒙版设置
    private func setupInputMaskView() {
        let maskView = UIView()
        // 恢复透明背景
        maskView.backgroundColor = UIColor.clear // 透明蒙版
        maskView.isHidden = true // 默认隐藏
        // 先不添加到视图，等containerView创建后再添加
        maskView.translatesAutoresizingMaskIntoConstraints = false

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(inputMaskTapped))
        tapGesture.delegate = self
        maskView.addGestureRecognizer(tapGesture)
        maskView.isUserInteractionEnabled = true

        inputMaskView = maskView

        print("[VideoComment] 输入框蒙版创建完成")
    }

    // 初始化时不添加蒙版，等到需要时再动态添加到containerView内部
    private func addInputMaskToView() {
        // 现在蒙版会在setupMaskInsideContainer中动态添加到containerView
        print("[VideoComment] 输入框蒙版将在需要时动态添加到containerView内部")
    }

    // 输入框蒙版点击处理
    @objc private func inputMaskTapped() {
        print("[VideoComment] 输入框蒙版被点击，当前状态: \(inputPanelState)")

        // 根据当前状态决定行为
        switch inputPanelState {
        case .keyboard:
            // 普通键盘状态：收起键盘
            print("[VideoComment] 收起键盘")
            inputFieldRef?.resignFirstResponder()
            inputPanelState = .none
        case .atPanel:
            // @面板状态：收起@面板，但保持键盘
            print("[VideoComment] 收起@面板，保持键盘")
            resetAtPanelState()
            inputPanelState = .keyboard
        case .emoji:
            // 表情面板状态：收起表情面板
            print("[VideoComment] 收起表情面板")
            inputPanelState = .none
        case .none:
            // 已收起状态：不做任何操作
            print("[VideoComment] 当前无输入面板，不做操作")
            break
            }
    }

    // MARK: - 输入框蒙版可见性管理
    private func updateInputMaskVisibility() {
        guard let maskView = inputMaskView else { return }

        switch inputPanelState {
        case .keyboard, .emoji, .atPanel:
            // 有输入相关面板时显示蒙版，防止误触评论列表
            maskView.isHidden = false

            // 重新设计：不改变containerView背景色，而是将蒙版添加到containerView内部
            // 这样蒙版只覆盖需要阻挡的区域，不影响整体UI
            setupMaskInsideContainer()

            print("[VideoComment] 显示输入框蒙版，添加到containerView内部")

        case .none:
            // 无输入面板时隐藏蒙版，允许正常交互评论列表
            maskView.isHidden = true
            print("[VideoComment] 隐藏输入框蒙版")
        }
    }

    // 将蒙版添加到containerView内部，只覆盖需要阻挡的区域
    private func setupMaskInsideContainer() {
        guard let maskView = inputMaskView else { return }

        // 如果蒙版不在containerView中，移动它
        if maskView.superview != containerView {
            maskView.removeFromSuperview()
            containerView.addSubview(maskView)

            // 重新设置约束，只覆盖评论列表区域
            maskView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                maskView.topAnchor.constraint(equalTo: headerView.bottomAnchor), // 从标题下方开始
                maskView.leftAnchor.constraint(equalTo: containerView.leftAnchor),
                maskView.rightAnchor.constraint(equalTo: containerView.rightAnchor),
                maskView.bottomAnchor.constraint(equalTo: decorativeInputBar.topAnchor) // 到输入框上方
            ])
        }

        // 确保蒙版在tableView之上，但在输入框之下
        containerView.bringSubviewToFront(maskView)

        // 确保输入框在蒙版之上
        containerView.bringSubviewToFront(decorativeInputBar)
        containerView.bringSubviewToFront(commentInputBar)

        // 表情键盘和@面板仍然在view层级，确保它们在最上层
        if let emojiView = emojiKeyboardView, !emojiView.isHidden {
            view.bringSubviewToFront(emojiView)
        }
        if let atPanelMask = atPanelMaskView, !atPanelMask.isHidden {
            view.bringSubviewToFront(atPanelMask)
        }
        if let atPanel = atPanelView, !atPanel.isHidden {
            view.bringSubviewToFront(atPanel)
        }
    }

    // MARK: - 确保层级正确（简化版）
    private func ensureInputViewsAboveMask() {
        // 现在蒙版在containerView内部，主要确保view层级的视图正确
        if let emojiView = emojiKeyboardView, !emojiView.isHidden {
            view.bringSubviewToFront(emojiView)      // 表情键盘
        }

        if let atPanelMask = atPanelMaskView, !atPanelMask.isHidden {
            view.bringSubviewToFront(atPanelMask)    // @面板蒙版
        }
        if let atPanel = atPanelView, !atPanel.isHidden {
            view.bringSubviewToFront(atPanel)        // @面板
        }

        print("[VideoComment] 确保view层级视图正确")
    }

    // MARK: - UI Setup
    private func setupUI() {
        // 修改背景为透明
        view.backgroundColor = .clear
        
        // 新增: 添加半透明背景遮罩视图
        backgroundView = UIView()
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.01)
        backgroundView.frame = self.view.bounds
        backgroundView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        view.addSubview(backgroundView)
        
        // 添加点击手势到背景遮罩
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        tapGesture.delegate = self
        backgroundView.addGestureRecognizer(tapGesture)

        // 创建输入框上方的蒙版
        setupInputMaskView()

        // containerView放在背景遮罩之上
        view.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            // 修改顶部距离为60pt
            make.top.equalTo(view.safeAreaLayoutGuide).offset(60)
            make.left.right.bottom.equalToSuperview()
        }

        // 在containerView创建后添加输入框蒙版，确保层级正确
        addInputMaskToView()

        // 添加灰色长条
        containerView.addSubview(pullIndicator)
        pullIndicator.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.centerX.equalToSuperview()
            make.width.equalTo(36)
            make.height.equalTo(4)
        }
        
        containerView.addSubview(headerView)
        headerView.snp.makeConstraints { make in
            // 调整header位置，在长条下方
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(80)
        }
        
        // 其他UI保持不变
        headerView.addSubview(titleLabel)
        headerView.addSubview(closeButton)
        
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        closeButton.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
        }
        
        // 添加装饰输入区（默认显示）
        containerView.addSubview(decorativeInputBar)
        decorativeInputBar.translatesAutoresizingMaskIntoConstraints = false
        decorativeInputBarBottomConstraint = decorativeInputBar.bottomAnchor.constraint(equalTo: containerView.bottomAnchor)
        decorativeInputBarHeightConstraint = decorativeInputBar.heightAnchor.constraint(equalToConstant: 65 + WindowUtil.safeAreaBottom)
        NSLayoutConstraint.activate([
            decorativeInputBar.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            decorativeInputBar.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            decorativeInputBarBottomConstraint!,
            decorativeInputBarHeightConstraint!
        ])

        // 添加真实输入区（默认隐藏）
        containerView.addSubview(commentInputBar)
        commentInputBar.translatesAutoresizingMaskIntoConstraints = false
        commentInputBar.isHidden = true // 默认隐藏
        commentInputBarBottomConstraint = commentInputBar.bottomAnchor.constraint(equalTo: containerView.bottomAnchor)
        commentInputBarHeightConstraint = commentInputBar.heightAnchor.constraint(equalToConstant: 132 + WindowUtil.safeAreaBottom)
        NSLayoutConstraint.activate([
            commentInputBar.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            commentInputBar.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            commentInputBarBottomConstraint!,
            commentInputBarHeightConstraint!
        ])
        
        tableView.tableFooterView = footerLabel
        // 确保tableView被add到containerView上
        containerView.addSubview(tableView)
        // tableView底部约束改为装饰输入区顶部
        tableView.snp.remakeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.left.right.equalTo(containerView)
            make.bottom.equalTo(decorativeInputBar.snp.top)
        }
        
        // 添加下拉手势
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        containerView.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Actions
    @objc private func closeButtonTapped() {
        dismiss(animated: true) {
            // 发送评论页面关闭通知
            NotificationCenter.default.post(name: NSNotification.Name("CommentViewControllerDismissed"), object: nil)
        }
    }
    
    @objc private func backgroundTapped() {
        // 优化背景点击逻辑：根据当前状态决定行为
        switch inputPanelState {
        case .keyboard:
            // 如果键盘弹出，第一次点击收起键盘
            inputPanelState = .none
        case .emoji:
            // 如果表情面板弹出，收起表情面板
            inputPanelState = .none
        case .atPanel:
            // @面板打开时，背景点击不做任何操作，避免意外收起键盘
            // 只有点击@面板上方的蒙版才能收起@面板
            return
        case .none:
            // 如果都收起了，关闭整个弹窗
            dismiss(animated: true) {
                // 发送评论页面关闭通知
                NotificationCenter.default.post(name: NSNotification.Name("CommentViewControllerDismissed"), object: nil)
            }
        }
    }

    // @面板上方蒙版点击处理
    @objc private func atPanelMaskTapped() {
        // 只隐藏@面板，但保持@监听状态，这样用户继续输入时还能再次显示@面板
        hideAtPanel()
        isAtPanelVisible = false
        inputPanelState = .keyboard
        print("[VideoComment] @面板蒙版被点击，隐藏@面板但保持@监听状态")
    }



    @objc private func decorativeInputTapped() {
        // 隐藏装饰输入框
        decorativeInputBar.isHidden = true

        // 显示真实输入框
        commentInputBar.isHidden = false

        // 更新tableView约束到真实输入框
        tableView.snp.remakeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.left.right.equalTo(containerView)
            make.bottom.equalTo(commentInputBar.snp.top)
        }

        // 聚焦输入框并弹出键盘
        inputFieldRef?.becomeFirstResponder()
        inputPanelState = .keyboard

        // 动画更新布局
        UIView.animate(withDuration: 0.3) {
            self.view.layoutIfNeeded()
        }
    }

    @objc private func decorativeLikeTapped() {
        // 防止重复点击
        decorativeLikeIcon.isUserInteractionEnabled = false

        // 确定操作值：1-执行点赞，2-取消点赞
        let operateValue = isVideoLiked ? 2 : 1
        let operateType = 1 // 1-点赞
        let worksIds = ["\(videoId)"]

        // 调用点赞接口
        APIManager.shared.doWorksLikeAndCollect(operateValue: operateValue, operateType: operateType, worksIds: worksIds) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.decorativeLikeIcon.isUserInteractionEnabled = true

                switch result {
                case .success(let response):
                    if response.status == 200 {
                        // 切换点赞状态
                        self.isVideoLiked.toggle()

                        // 更新点赞数量
                        if self.isVideoLiked {
                            self.videoLikeCount += 1
                        } else {
                            self.videoLikeCount = max(0, self.videoLikeCount - 1)
                        }

                        // 更新UI
                        self.decorativeLikeIcon.image = UIImage(named: self.isVideoLiked ? "video_like_selected" : "video_like")
                        self.decorativeLikeCountLabel.text = "\(self.videoLikeCount)"

                        // 添加点击动画效果
                        self.animateIconTap(self.decorativeLikeIcon)

                        // 显示提示
                        self.showToast(self.isVideoLiked ? "点赞成功" : "取消点赞成功")

                        // 同步数据到视频页面
                        self.onVideoDataChanged?(self.videoLikeCount, self.videoCollectCount, self.isVideoLiked, self.isVideoCollected)

                    } else {
                        self.showToast(response.displayMessage.isEmpty ? "操作失败" : response.displayMessage)
                    }
                case .failure(let error):
                    self.showToast("网络错误：\(error.localizedDescription)")
                }
            }
        }
    }

    @objc private func decorativeCollectTapped() {
        // 防止重复点击
        decorativeCollectIcon.isUserInteractionEnabled = false

        // 确定操作值：1-执行收藏，2-取消收藏
        let operateValue = isVideoCollected ? 2 : 1
        let operateType = 2 // 2-收藏
        let worksIds = ["\(videoId)"]

        // 调用收藏接口
        APIManager.shared.doWorksLikeAndCollect(operateValue: operateValue, operateType: operateType, worksIds: worksIds) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }
                self.decorativeCollectIcon.isUserInteractionEnabled = true

                switch result {
                case .success(let response):
                    if response.status == 200 {
                        // 切换收藏状态
                        self.isVideoCollected.toggle()

                        // 更新收藏数量
                        if self.isVideoCollected {
                            self.videoCollectCount += 1
                        } else {
                            self.videoCollectCount = max(0, self.videoCollectCount - 1)
                        }

                        // 更新UI
                        self.decorativeCollectIcon.image = UIImage(named: self.isVideoCollected ? "video_collect_selected" : "video_collect")
                        self.decorativeCollectCountLabel.text = "\(self.videoCollectCount)"

                        // 添加点击动画效果
                        self.animateIconTap(self.decorativeCollectIcon)

                        // 显示提示
                        self.showToast(self.isVideoCollected ? "收藏成功" : "取消收藏成功")

                        // 同步数据到视频页面
                        self.onVideoDataChanged?(self.videoLikeCount, self.videoCollectCount, self.isVideoLiked, self.isVideoCollected)

                    } else {
                        self.showToast(response.errMsg.isEmpty ? "操作失败" : response.errMsg)
                    }
                case .failure(let error):
                    self.showToast("网络错误：\(error.localizedDescription)")
                }
            }
        }
    }

    // 图标点击动画效果
    private func animateIconTap(_ iconView: UIImageView) {
        UIView.animate(withDuration: 0.1, animations: {
            iconView.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                iconView.transform = .identity
            }
        }
    }
    
    // MARK: - Data Loading
    func showComments(videoId: Int) {
        // 更新当前视频ID
        self.videoId = videoId

        // 重置页码和数据状态
        self.pageSize = 20
        self.lastCommentId = nil
        self.lastCreateTime = nil
        self.comments = []
        self.hasMoreData = true

        // 保持当前标题不变，避免闪动
        // 标题会在loadCommentCount()的API回调中更新为最新数量

        // 获取评论数量并加载第一页评论
        loadCommentCount()
        loadComments()
    }

    // 更新视频数据（点赞数、收藏数、状态）
    func updateVideoData(likeCount: Int, collectCount: Int, isLiked: Bool = false, isCollected: Bool = false) {
        self.videoLikeCount = likeCount
        self.videoCollectCount = collectCount
        self.isVideoLiked = isLiked
        self.isVideoCollected = isCollected

        // 更新装饰输入框中的数据显示
        decorativeLikeCountLabel?.text = "\(likeCount)"
        decorativeCollectCountLabel?.text = "\(collectCount)"

        // 更新图标状态
        decorativeLikeIcon?.image = UIImage(named: isLiked ? "video_like_selected" : "video_like")
        decorativeCollectIcon?.image = UIImage(named: isCollected ? "video_collect_selected" : "video_collect")
    }

    // MARK: - 评论数量管理

    /// 获取评论数量
    private func loadCommentCount() {
        APIManager.shared.getCommentNumber(worksId: videoId) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.updateCommentCount(response.data)
                    } else {
                        print("获取评论数量失败：\(response.displayMessage)")
                    }
                case .failure(let error):
                    print("获取评论数量网络错误：\(error.localizedDescription)")
                }
            }
        }
    }

    /// 更新评论数量显示
    private func updateCommentCount(_ count: Int) {
        commentCount = count
        titleLabel.text = "评论 \(commentCount)"
    }

    /// 刷新评论数量（发送评论后调用）
    private func refreshCommentCount() {
        loadCommentCount()
    }

    // 保留原有loadComments作为内部方法
    private func loadComments() {
        guard !isLoading && hasMoreData else { return }
        isLoading = true
        
        print("[CommentVC] 开始加载评论，videoId=\(videoId), fromId=\(self.lastCommentId ?? 0), createTime=\(self.lastCreateTime ?? "")")

        APIManager.shared.getVideoComment(
            worksId: videoId,
            size: pageSize,
            pid: nil,
            excludeId: nil,
            fromId: lastCommentId,
            createTime: lastCreateTime
        ) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoading = false
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        let items = response.data.list
                        // 提取全局 mentionedUser
                        var globalMap: [String:String] = [:]
                        if let anyMap = response.data.mentionedUser {
                            if let dict = anyMap as? [String:String] {
                                globalMap = dict
                            } else if let dict = anyMap as? [String:Any] {
                                for (k,v) in dict { if let name = v as? String { globalMap[k] = name } }
                            }
                        }
                        let models = items.map { self.convertToCommentModel($0, level: 0, mentionMapping: globalMap) }

                        if self.comments.isEmpty {
                            self.comments = models
                        } else {
                            self.comments += models
                        }

                        // 更新分页游标
                        if let last = items.last {
                            self.lastCommentId = last.id
                            self.lastCreateTime = last.createTime
                        }

                        // 判断是否还有更多
                        self.hasMoreData = !items.isEmpty
                        self.footerLabel.isHidden = self.hasMoreData

                        // 接口已不返回 total，不再更新标题
                        self.tableView.reloadData()
                        
                        // 如果有数据但tableView没显示，检查布局问题
                        if !self.comments.isEmpty && self.tableView.visibleCells.isEmpty {
                            print("[CommentVC] 警告: 有评论数据但没有可见单元格，可能存在布局问题")
                            print("[CommentVC] tableView frame: \(self.tableView.frame)")
                            print("[CommentVC] tableView contentSize: \(self.tableView.contentSize)")
                        }
                    } else {
                        print("[CommentVC] 获取评论失败: \(response.errMsg)")
                        self.hasMoreData = false
                    }
                case .failure(let error):
                    print("[CommentVC] 加载评论错误: \(error)")
                    self.hasMoreData = false
                }
            }
        }
    }
    
    // MARK: - Mapping
    private func convertToCommentModel(_ item: CommentItem, level: Int = 0, mentionMapping: [String:String] = [:]) -> CommentModel {
        let avatar = item.commentUserVo?.wxAvator ?? "default_avatar"
        let username = item.commentUserVo?.nickName ?? "匿名用户"
        let customerId = item.commentUserVo?.customerId ?? ""

        // 处理 @ 提及
        var mapping: [String: String] = mentionMapping
        // 如果 item 内也带有 mentionedUser，则优先合并/覆盖
        if let mapAny = item.mentionedUser {
            if let dict = mapAny as? [String: String] {
                mapping.merge(dict) { _, new in new }
            } else if let dict = mapAny as? [String: Any] {
                for (k, v) in dict { if let name = v as? String { mapping[k] = name } }
            }
        }
        let mentionResult = parseMentions(in: item.commentDesc, mapping: mapping)
        let displayText = mentionResult.0
        let mentionRanges = mentionResult.1
        let replyTo = item.pcommentUserVo?.nickName
        // 判断是否为作者本人评论（根据 commentUserVo.customerId 与作品作者 ID 比较，可根据业务需要调整）
        // 使用接口新增字段 isCommentCustomerWorks 判定是否为作者评论
        let isAuthorComment = item.isCommentCustomerWorks

        let showExpandBtn = item.isHasChild && item.childCount > 0

        var model = CommentModel(
            id: String(item.id),
            customerId: customerId,
            avatar: avatar,
            username: username,
            isAuthor: isAuthorComment,
            content: displayText,
            timestamp: formatRelativeTime(item.createTime),
            likes: item.likeNumber,
            dislikes: item.notLikeNumber,
            isLiked: item.likeState == 1,
            isDisliked: item.notLikeState == 1,
            imageUrl: parseFirstImageURL(from: item.commentImg),
            replyTo: replyTo,
            address: item.address,
            isHasChild: item.isHasChild,
            childCount: item.childCount,
            replies: nil,
            showExpandReplies: showExpandBtn,
            level: level,
            cellHeight: 0,
            mentionRanges: mentionRanges,
            isMine: item.isMyComment
        )

        model.cellHeight = calculateCellHeight(text: displayText, hasImage: (model.imageUrl != nil && !(model.imageUrl!.isEmpty)), level: level, showExpandReplies: showExpandBtn)
        return model
    }

    // MARK: - 解析 commentImg
    private func parseFirstImageURL(from jsonArrayString: String?) -> String? {
        guard let jsonArrayString = jsonArrayString, !jsonArrayString.isEmpty else { return nil }
        // 如果不是 '[' 开头，直接返回原字符串（兼容老数据）
        guard jsonArrayString.trimmingCharacters(in: .whitespaces).first == "[" else {
            return jsonArrayString
        }
        // 尝试解析 JSON 数组
        if let data = jsonArrayString.data(using: .utf8),
           let array = try? JSONSerialization.jsonObject(with: data, options: []) as? [String],
           let first = array.first, !first.isEmpty {
            return first
        }
        return nil
    }

    // MARK: - 时间格式化："刚刚" / "x分钟前" / "x小时前" / "x天前" / "MM-dd" / "yyyy-MM-dd"
    private func formatRelativeTime(_ timeString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        formatter.locale = Locale(identifier: "zh_CN")
        guard let date = formatter.date(from: timeString) else { return timeString }
        let now = Date()
        let interval = now.timeIntervalSince(date)
        if interval < 60 { return "刚刚" }
        let minute = 60.0
        let hour = 3600.0
        let day = 86400.0
        if interval < hour { return "\(Int(interval/minute))分钟前" }
        if interval < day { return "\(Int(interval/hour))小时前" }
        if interval < day * 7 { return "\(Int(interval/day))天前" }
        // 当年显示月日，否则完整年月日
        let output = DateFormatter()
        output.locale = formatter.locale
        if Calendar.current.isDate(date, equalTo: now, toGranularity: .year) {
            output.dateFormat = "MM-dd"
        } else {
            output.dateFormat = "yyyy-MM-dd"
        }
        return output.string(from: date)
    }

    /// 计算行高：
    /// - Parameters:
    ///   - text: 评论文本
    ///   - hasImage: 是否包含图片
    ///   - level: 评论层级（0=一级，1=二级）
    ///   - showExpandReplies: 是否展示"展开N条回复"按钮
    private func calculateCellHeight(text: String, hasImage: Bool, level: Int, showExpandReplies: Bool = false) -> CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let textWidth: CGFloat
        if level == 0 {
            // 右侧与屏幕间距 16，与头像距离 40+12，共 68
            textWidth = screenWidth - 16 - 40 - 12 - 16
        } else {
            textWidth = screenWidth - 16 - 32 - 8 - 44
        }
        let font = UIFont.systemFont(ofSize: 14)
        let textHeight = (text as NSString).boundingRect(
            with: CGSize(width: textWidth, height: CGFloat.greatestFiniteMagnitude),
            options: [.usesLineFragmentOrigin, .usesFontLeading],
            attributes: [.font: font],
            context: nil
        ).height

        var height: CGFloat = 0
        if level == 0 {
            height = 16 + 20 + 8 + textHeight // 顶部 + 用户名行 + 间距 + 文本
            if hasImage { height += 8 + 68 }
            // 时间行：与上方间距 8 + label 高度 12
            height += 8 + 12
            // 展开回复按钮已移出 CommentCell，不再计算
            height += 8 // bottom margin
        } else {
            height = 0 + 20 + 6 + textHeight
            if hasImage { height += 6 + 60 }
            height += 4
        }
        // 最小高度保护，避免信息被裁剪
        if height < 60 { height = 60 }
        return ceil(height)
    }
    
    private func setupKeyboardObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillShow),
            name: UIResponder.keyboardWillShowNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillHide),
            name: UIResponder.keyboardWillHideNotification,
            object: nil
        )
    }
    
    @objc private func keyboardWillShow(_ notification: Notification) {
        guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else { return }
        let keyboardHeight = keyboardFrame.height
        lastKeyboardHeight = keyboardHeight
        if inputPanelState == .none { // 仅在当前非键盘/自定义面板状态时切换
            inputPanelState = .keyboard
        }

        // 更新输入框位置
        commentInputBarBottomConstraint?.constant = -keyboardHeight

        // 输入框蒙版使用全屏覆盖，不需要动态调整

        // 更新@面板位置，跟随输入框
        let inputBarHeight: CGFloat = 132 // 输入框高度
        atPanelBottomConstraint?.constant = -(keyboardHeight + inputBarHeight)

        // 更新@面板蒙版位置，到@面板顶部
        atPanelMaskBottomConstraint?.constant = -(keyboardHeight + inputBarHeight + 104) // 104是@面板高度

        UIView.animate(withDuration: 0.3) {
            self.view.layoutIfNeeded()
        } completion: { _ in
            // 布局完成后，重新确保层级顺序，防止输入框被蒙版遮挡
            self.ensureInputViewsAboveMask()
        }
    }
    
    @objc private func keyboardWillHide(_ notification: Notification) {
        if inputPanelState == .emoji {
            // 表情面板状态：不回到底部，等待表情面板弹出
        } else if inputPanelState == .atPanel {
            // @面板状态：完全阻止键盘收起，不做任何操作
            print("[VideoComment] @面板打开时阻止键盘收起")
            // 不执行任何操作，保持当前状态
            return
        } else {
            // 普通键盘状态：键盘隐藏时清理@状态
            resetAtPanelState()
            inputPanelState = .none
        }
    }
    
    // 添加下拉手势处理
    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: view)
        let velocity = gesture.velocity(in: view)
        
        switch gesture.state {
        case .changed:
            // 只允许向下拖动
            if translation.y > 0 {
                containerView.transform = CGAffineTransform(translationX: 0, y: translation.y)
            }
            
        case .ended:
            // 如果下拉速度够快或者下拉距离超过一定值，就关闭视图
            if velocity.y > 1000 || translation.y > 200 {
                UIView.animate(withDuration: 0.3, animations: {
                    self.containerView.transform = CGAffineTransform(translationX: 0, y: self.containerView.frame.height)
                }) { _ in
                    self.dismiss(animated: false)
                }
            } else {
                // 否则回弹
                UIView.animate(withDuration: 0.3) {
                    self.containerView.transform = .identity
                }
            }
            
        default:
            break
        }
    }
    
    // MARK: - 发送按钮事件
     @objc private func sendButtonTapped() {
         guard !isSending else { return }
         let rawText = inputFieldRef?.text ?? ""
         let contentText = processContentForSending(from: rawText).trimmingCharacters(in: .whitespacesAndNewlines)
         // 没有文字且没有选图 → 不发送
         guard !contentText.isEmpty || selectedImage != nil else { return }

         // 发送前先收起所有键盘和面板
         inputPanelState = .none

         isSending = true

         // 显示 HUD
         let loadingHUD = UIActivityIndicatorView(style: .large)
         loadingHUD.color = UIColor(hex: "#FF6236")
         loadingHUD.center = view.center
         loadingHUD.startAnimating()
         view.addSubview(loadingHUD)

         // 内部闭包：真正调用发送 API
         func performSend(with imageURLs: [String]?) {
             if let pid = replyPid {
                 APIManager.shared.replyComment(worksId: videoId, pid: pid, commentDesc: contentText, commentImg: imageURLs, pcustomerId: replyPcustomerId) { [weak self] result in
                     self?.handleReplySendResult(result)
                 }
             } else {
                 APIManager.shared.sendComment(worksId: videoId, commentDesc: contentText, commentImg: imageURLs) { [weak self] result in
                     self?.handleCommentSendResult(result, onSuccess: {
                         self?.refreshAfterSend()
                     })
                 }
             }
         }

         // 发送完成统一移除 HUD
         func finishLoading() {
             DispatchQueue.main.async { loadingHUD.removeFromSuperview() }
         }

         // 如果携带图片，先上传至七牛
         if let img = selectedImage, let data = img.jpegData(compressionQuality: 0.85) {
             APIManager.shared.uploadFileQNRaw(files: [data]) { [weak self] uploadResult in
                 switch uploadResult {
                 case .success(let resp):
                     if resp.status == 200, let url = resp.data?.first?.data.first {
                         performSend(with: [url])
                         finishLoading()
                     } else {
                         DispatchQueue.main.async { self?.showToast(resp.displayMessage.isEmpty ? "图片上传失败" : resp.displayMessage) }
                         self?.isSending = false
                         finishLoading()
                     }
                 case .failure(let error):
                     DispatchQueue.main.async { self?.showToast("图片上传失败：\(error.localizedDescription)") }
                     finishLoading()
                     self?.isSending = false
                 }
             }
         } else {
             performSend(with: nil)
             finishLoading()
         }
     }

    // 处理发送成功或失败（无返回数据，仅提示）
    private func handleCommentSendResult(_ result: Result<BaseResponse, APIError>, onSuccess: @escaping () -> Void) {
        DispatchQueue.main.async {
            self.isSending = false
            switch result {
            case .success(let resp):
                if resp.status == 200 {
                    // 清理输入框内容
                    self.inputFieldRef?.text = ""
                    self.selectedImage = nil
                    self.removeImagePreview()
                    self.updateSendButtonState()

                    // 发送成功后保持键盘收起状态，不重新聚焦
                    // inputPanelState 已经在发送前设置为 .none，这里不需要改变

                    self.showToast("发送成功")
                    // 刷新评论数量
                    self.refreshCommentCount()
                    onSuccess()
                } else {
                    self.showToast(resp.displayMessage.isEmpty ? "发送失败" : resp.displayMessage)
                }
            case .failure(let error):
                self.showToast("发送失败：\(error.localizedDescription)")
            }
        }
    }

    // 处理回复评论的结果（需要用返回数据插入本地列表）
    private func handleReplySendResult(_ result: Result<CommentSingleResponse, APIError>) {
        DispatchQueue.main.async {
            self.isSending = false
            switch result {
            case .success(let resp):
                if resp.status == 200 {
                    // 清理输入框内容
                    self.inputFieldRef?.text = ""
                    self.selectedImage = nil
                    self.removeImagePreview()
                    self.updateSendButtonState()

                    // 发送成功后保持键盘收起状态，不重新聚焦
                    // inputPanelState 已经在发送前设置为 .none，这里不需要改变

                    self.showToast("发送成功")
                    // 刷新评论数量
                    self.refreshCommentCount()
                    // 将服务器返回的数据转换为 CommentModel
                    let model = self.convertToCommentModel(resp.data, level: 1)
                    self.insertLocalReply(model: model)
                } else {
                    self.showToast(resp.displayMessage.isEmpty ? "发送失败" : resp.displayMessage)
                }
            case .failure(let error):
                self.showToast("发送失败：\(error.localizedDescription)")
            }
        }
    }

    private func clearInputAfterSend() {
        inputFieldRef?.text = ""
        selectedImage = nil
        removeImagePreview()
        inputPanelState = .none
        // 更新发送按钮状态
        updateSendButtonState()
    }



    private func refreshAfterSend() {
        // 刷新整个列表显示最新评论
        comments.removeAll()
        lastCommentId = nil
        lastCreateTime = nil
        hasMoreData = true
        tableView.reloadData()
        loadComments()
    }

    // 本地插入回复
    private func insertLocalReply(model: CommentModel) {
        guard let parentIdx = selectedParentIndex else { return }
        var modelVar = model
        modelVar.cellHeight = calculateCellHeight(text: model.content, hasImage: false, level: 1)

        var insertIndex = parentIdx + 1
        while insertIndex < comments.count && comments[insertIndex].level == 1 {
            insertIndex += 1
        }
        comments.insert(modelVar, at: insertIndex)

        tableView.beginUpdates()
        tableView.insertRows(at: [IndexPath(row: insertIndex, section: 0)], with: .automatic)
        // reload parent to update button frame
        tableView.reloadRows(at: [IndexPath(row: parentIdx, section: 0)], with: .none)
        tableView.endUpdates()

        // 滚动到新回复可见
        tableView.scrollToRow(at: IndexPath(row: insertIndex, section: 0), at: .middle, animated: true)

        // 重置回复状态
        replyPid = nil
        replyTargetUsername = nil
        selectedParentIndex = nil
        clickedIndex = nil
    }
    
    // MARK: - 表情键盘切换逻辑
    @objc private func emojiButtonTapped() {
        inputPanelState = (inputPanelState == .emoji) ? .keyboard : .emoji
    }
    private func showEmojiKeyboard() {
        if emojiKeyboardView == nil {
            let emojiView = EmojiKeyboardView()
            emojiView.emojiSelected = { [weak self] emoji in
                self?.insertEmoji(emoji)
            }
            emojiKeyboardView = emojiView
            view.addSubview(emojiView)
            emojiView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                emojiView.leftAnchor.constraint(equalTo: view.leftAnchor),
                emojiView.rightAnchor.constraint(equalTo: view.rightAnchor),
                emojiView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
                emojiView.heightAnchor.constraint(equalToConstant: lastKeyboardHeight)
            ])
            // 立即隐藏并布局，防止初次动画
            emojiView.isHidden = true
            view.layoutIfNeeded()
        }
        // 只在未显示时做动画
        if emojiKeyboardView?.isHidden ?? true {
            emojiKeyboardView?.alpha = 0
            emojiKeyboardView?.isHidden = false
            commentInputBarBottomConstraint?.constant = -lastKeyboardHeight
            isEmojiKeyboardVisible = true
            UIView.animate(withDuration: 0.25) {
                self.emojiKeyboardView?.alpha = 1
                self.view.layoutIfNeeded()
            }
        }
    }
    private func hideEmojiKeyboard(animated: Bool) {
        guard let emojiKeyboardView = emojiKeyboardView, !emojiKeyboardView.isHidden else { return }
        if animated {
            UIView.animate(withDuration: 0.25, animations: {
                emojiKeyboardView.alpha = 0
                self.view.layoutIfNeeded()
            }) { _ in
                emojiKeyboardView.isHidden = true
                self.isEmojiKeyboardVisible = false
                self.inputFieldRef?.becomeFirstResponder()
            }
        } else {
            emojiKeyboardView.isHidden = true
            self.isEmojiKeyboardVisible = false
            self.inputFieldRef?.becomeFirstResponder()
        }
    }
    private func insertEmoji(_ emoji: String) {
        guard let inputField = inputFieldRef else { return }
        if let selectedRange = inputField.selectedTextRange {
            inputField.replace(selectedRange, withText: emoji)
        } else {
            inputField.text.append(emoji)
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    private func updateInputPanelState(from old: InputPanelState, to new: InputPanelState) {
        // 动态调整输入bar高度
        let baseHeight: CGFloat = 132 + (selectedImage != nil ? previewExtraHeight : 0)
        switch new {
        case .none:
            // 收起键盘时，显示装饰输入框，隐藏真实输入框
            commentInputBarHeightConstraint?.constant = baseHeight + WindowUtil.safeAreaBottom
            inputFieldRef?.resignFirstResponder()
            hideEmojiKeyboard(animated: true)
            hideAtPanel() // 确保@面板也被隐藏
            commentInputBarBottomConstraint?.constant = 0
            // 重置占位文字 & 回复目标
            replyTargetUsername = nil
            // 重置@提及状态
            resetAtPanelState()

            // 显示装饰输入框，隐藏真实输入框
            decorativeInputBar.isHidden = false
            commentInputBar.isHidden = true

            // 更新tableView约束到装饰输入框
            tableView.snp.remakeConstraints { make in
                make.top.equalTo(headerView.snp.bottom)
                make.left.right.equalTo(containerView)
                make.bottom.equalTo(decorativeInputBar.snp.top)
            }

            UIView.animate(withDuration: 0.25) { self.view.layoutIfNeeded() }
        case .keyboard:
            commentInputBarHeightConstraint?.constant = baseHeight
            inputFieldRef?.becomeFirstResponder()
            if old == .emoji { // 如果是从表情盘切换到键盘
                hideEmojiKeyboard(animated: true) // 显式收起表情盘
            }
            if old != .atPanel { // 如果不是从@面板切换过来，则隐藏@面板
                hideAtPanel()
            }

            // 显示真实输入框，隐藏装饰输入框
            decorativeInputBar.isHidden = true
            commentInputBar.isHidden = false

            // 更新tableView约束到真实输入框
            tableView.snp.remakeConstraints { make in
                make.top.equalTo(headerView.snp.bottom)
                make.left.right.equalTo(containerView)
                make.bottom.equalTo(commentInputBar.snp.top)
            }
        case .emoji:
            commentInputBarHeightConstraint?.constant = baseHeight
            inputFieldRef?.resignFirstResponder()
            showEmojiKeyboard()
            resetAtPanelState() // 切换到表情面板时重置@状态

            // 显示真实输入框，隐藏装饰输入框
            decorativeInputBar.isHidden = true
            commentInputBar.isHidden = false

            // 更新tableView约束到真实输入框
            tableView.snp.remakeConstraints { make in
                make.top.equalTo(headerView.snp.bottom)
                make.left.right.equalTo(containerView)
                make.bottom.equalTo(commentInputBar.snp.top)
            }
        case .atPanel:
            commentInputBarHeightConstraint?.constant = baseHeight
            inputFieldRef?.becomeFirstResponder() // 保持键盘
            hideEmojiKeyboard(animated: true)
            showAtPanel()

            // 显示真实输入框，隐藏装饰输入框
            decorativeInputBar.isHidden = true
            commentInputBar.isHidden = false

            // 更新tableView约束到真实输入框
            tableView.snp.remakeConstraints { make in
                make.top.equalTo(headerView.snp.bottom)
                make.left.right.equalTo(containerView)
                make.bottom.equalTo(commentInputBar.snp.top)
            }
        }
    }
    
    // MARK: - 输入框事件处理 & Mention 删除控制
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        // 回车发送
        if text == "\n" {
            sendButtonTapped()
            return false
        }

        guard let attrStr = textView.attributedText else { return true }

        // 处理删除逻辑：text 为空表示删除
        if text.isEmpty {
            var targetRange = range
            // backspace 情况：range.length == 0
            if range.length == 0, range.location > 0 {
                targetRange.location -= 1
                targetRange.length = 1
            }

            // 检查是否落在 mentionId 富文本范围
            let mentionAttr = NSAttributedString.Key("mentionId")
            // 越界保护
            guard targetRange.location >= 0 && targetRange.location < attrStr.length else { return true }
            var effective = NSRange(location: 0, length: 0)
            if attrStr.attribute(mentionAttr, at: targetRange.location, effectiveRange: &effective) != nil {
                // 扩展：同时删除尾部零宽空格(若存在)
                let nextCharIndex = effective.location + effective.length
                let hasZeroWidthSpace = nextCharIndex < attrStr.length && (attrStr.string as NSString).character(at: nextCharIndex) == 0x200B
                let fullRange = NSRange(location: effective.location, length: hasZeroWidthSpace ? effective.length + 1 : effective.length)
                let storage = textView.textStorage
                storage.beginEditing()
                storage.replaceCharacters(in: fullRange, with: "")
                storage.endEditing()

                // 重置光标位置的文本属性为默认样式
                let cursorLocation = effective.location
                textView.selectedRange = NSRange(location: cursorLocation, length: 0)

                // 重置输入属性为默认样式，防止后续输入继承@用户名的蓝色样式
                textView.typingAttributes = [
                    .font: UIFont.systemFont(ofSize: 14),
                    .foregroundColor: UIColor.black
                ]

                rebuildMentionDict()
                return false
            }
        } else {
            // 禁止在 mention 高亮块内部插入内容
            let mentionAttr = NSAttributedString.Key("mentionId")
            guard range.location >= 0 && range.location < attrStr.length else { return true }
            var eff = NSRange(location: 0, length: 0)
            if attrStr.attribute(mentionAttr, at: range.location, effectiveRange: &eff) != nil {
                return false
            }
        }
        return true
    }

    private func rebuildMentionDict() {
        mentionedUserDict.removeAll()
        guard let attr = inputFieldRef?.attributedText else { return }
        attr.enumerateAttribute(NSAttributedString.Key("mentionId"), in: NSRange(location: 0, length: attr.length), options: []) { value, range, _ in
            if let id = value as? String {
                let raw = (attr.string as NSString).substring(with: range)
                let trimmed = raw.replacingOccurrences(of: "@", with: "").trimmingCharacters(in: CharacterSet(charactersIn: "\u{200B}"))
                mentionedUserDict[id] = trimmed
            }
        }
    }

    // MARK: - UITableViewDelegate & DataSource
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return cachedRowMap.count
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        let item = cachedRowMap[indexPath.row]
        switch item.0 {
        case .expand:
            return 32 // 固定高度
        default:
            // 直接交由 Auto Layout 计算高度，避免手动估算值过小导致内容被压缩
            return UITableView.automaticDimension
        }
    }

    // 新增：数据源必备方法，返回具体 Cell
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let item = cachedRowMap[indexPath.row]
        let model = comments[item.1]
        switch item.0 {
        case .comment:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "CommentCell", for: indexPath) as? CommentCell else { return UITableViewCell() }
            cell.configure(with: model)
            // 头像点击回调
            cell.avatarTapCallback = { [weak self] in
                self?.navigateToPersonalHomepage(userId: model.customerId)
            }
            // @用户名点击回调
            cell.mentionTapCallback = { [weak self] userId in
                self?.navigateToPersonalHomepage(userId: userId)
            }
            cell.likeCallback = { [weak self] in
                self?.toggleLike(forRow: indexPath.row)
            }
            cell.dislikeCallback = { [weak self] in
                self?.toggleDislike(forRow: indexPath.row)
            }
            // 长按弹窗
            cell.longPressCallback = { [weak self] in
                guard let self = self else { return }
                let isOwner = model.isMine
                let actions: [CommentActionType] = [.reply, .copy, .report, .delete]
                let sheet = CommentActionSheet(actions: actions, isOwner: isOwner) { [weak self] action in
                    self?.handleCommentAction(action, for: model, at: indexPath)
                }
                sheet.show(in: self.view)
            }
            return cell
        case .expand:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "ExpandRepliesCell", for: indexPath) as? ExpandRepliesCell else { return UITableViewCell() }
            let loaded = self.replyCursorTracker[model.id]?.loadedCount ?? 0
            let remaining = max(model.childCount - loaded, 0)
            let title = loaded == 0 ? "展开\(remaining)条回复↓" : "展开更多"
            cell.configure(title: title)
            cell.tapCallback = { [weak self] in
                self?.handleExpandReplies(forParentIndex: item.1)
            }
            return cell
        case .reply:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: replyCellIdentifier, for: indexPath) as? CommentReplyCell else { return UITableViewCell() }
            cell.configure(with: model)
            cell.avatarTapCallback = { [weak self] in
                self?.navigateToPersonalHomepage(userId: model.customerId)
            }
            // @用户名点击回调
            cell.mentionTapCallback = { [weak self] userId in
                self?.navigateToPersonalHomepage(userId: userId)
            }
            cell.likeCallback = { [weak self] in
                self?.toggleLike(forRow: indexPath.row)
            }
            cell.dislikeCallback = { [weak self] in
                self?.toggleDislike(forRow: indexPath.row)
            }
            // 长按弹窗
            cell.longPressCallback = { [weak self] in
                guard let self = self else { return }
                let isOwner = model.isMine
                let actions: [CommentActionType] = [.reply, .copy, .report, .delete]
                let sheet = CommentActionSheet(actions: actions, isOwner: isOwner) { [weak self] action in
                    self?.handleCommentAction(action, for: model, at: indexPath)
                }
                sheet.show(in: self.view)
            }
            return cell
        }
    }

    // 新增：去除section header/footer间距
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 0.01
    }
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 0.01
    }

    // 选中评论进行回复
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let mapItem = cachedRowMap[indexPath.row]
        if mapItem.0 == .expand { return } // 展开按钮点击已在回调处理
        let realIndex = mapItem.1
        guard realIndex < comments.count else { return }
        let model = comments[realIndex]
        clickedIndex = indexPath.row
        // 确定父评论（一级）下标
        var parentIndex = realIndex
        if model.level == 1 {
            var idx = realIndex
            while idx >= 0 && idx < comments.count {
                if comments[idx].level == 0 { parentIndex = idx; break }
                idx -= 1
            }
        }
        selectedParentIndex = parentIndex

        if model.level == 0 {
            // 回复一级 -> 二级评论
            replyTargetUsername = nil
            replyPid = Int(model.id)
            replyPcustomerId = nil
        } else {
            // 回复二级 -> 逻辑也是二级评论，但带 @ 对象
            replyTargetUsername = model.username
            replyPid = Int(comments[parentIndex].id) // 始终传一级 pid
            replyPcustomerId = model.customerId
        }
        // 聚焦输入框并弹出键盘
        inputPanelState = .keyboard

        // 延迟滚动，确保键盘弹出后被回复的评论仍然可见
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            if indexPath.row < self.tableView.numberOfRows(inSection: 0) {
                self.tableView.scrollToRow(at: indexPath, at: .top, animated: true)
            }
        }
    }
    
    // 监听所有滚动视图的滚动：
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // 评论列表的上拉加载更多
        if scrollView == tableView {
            let offsetY = scrollView.contentOffset.y
            let contentHeight = scrollView.contentSize.height
            let height = scrollView.frame.size.height
            if offsetY > contentHeight - height - 100 {
                loadComments()
            }
            return
        }

        // @ 用户搜索面板的水平分页加载
        if let mentionCollectionView = mentionCollectionView, scrollView == mentionCollectionView {
            let offsetX = scrollView.contentOffset.x + scrollView.bounds.width
            if mentionHasMore && offsetX > scrollView.contentSize.width - 40 {
                mentionPage += 1
                searchMentionUsers(keyword: mentionKeyword, page: mentionPage)
            }
            return
        }
    }
    
    // MARK: - UIGestureRecognizerDelegate
    // 修改手势代理方法
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        // 获取手势所属的视图
        let gestureView = gestureRecognizer.view

        // 如果是输入框蒙版的手势
        if gestureView == inputMaskView {
            // 输入框蒙版手势：允许执行
            return true
        }

        // 如果是@面板蒙版的手势
        if gestureView == atPanelMaskView {
            // @面板蒙版手势：允许执行
            return true
        }

        // 如果是@面板内部的手势
        if gestureView == atPanelView {
            // @面板内部的所有点击都不应该收起键盘，只是防止穿透
            // 不拦截CollectionView的交互
            return false
        }

        // 如果是背景遮罩的手势
        if gestureView == backgroundView {
            // 有输入面板时，背景遮罩不处理，由输入框蒙版处理
            if inputPanelState != .none {
                return false
            }

            // 无输入面板时，允许背景遮罩关闭弹窗
            return touch.view == self.backgroundView
        }

        // 其他手势：根据触摸的视图判断
        return touch.view == self.backgroundView
    }
    
    // MARK: - UITextViewDelegate
    func textViewDidBeginEditing(_ textView: UITextView) {
        // 输入框获得焦点时，强制切换到键盘状态
        inputPanelState = .keyboard
    }

    func textViewDidEndEditing(_ textView: UITextView) {
        // 输入框失去焦点时，只有在非@面板状态下才清理@状态
        // @面板打开时，不应该因为失去焦点而清理状态
        if inputPanelState != .atPanel {
            resetAtPanelState()
        }
        // 移除自动重新获取焦点的逻辑，避免键盘收起问题
    }
    
    // MARK: - 展开二级评论
    private func handleExpandReplies(at indexPath: IndexPath) {
        guard indexPath.row < comments.count else { return }
        var parentComment = comments[indexPath.row]
        // 取父评论的分页游标（fromId / createTime）和已加载数量
        let cursor = replyCursorTracker[parentComment.id] ?? (nil, nil, 0)
        let fromIdForReply = cursor.lastId
        let createTimeForReply = cursor.lastTime
        let loadedCount = cursor.loadedCount
        // 第一次展开拉取 3 条，其后每次拉取 10 条
        let pageSizeForReply = (loadedCount == 0) ? 3 : 10
        // 转换 parentComment.id 为 Int
        guard let parentIdInt = Int(parentComment.id) else { return }
        isLoading = true
        APIManager.shared.getVideoComment(
            worksId: videoId,
            size: pageSizeForReply,
            pid: parentIdInt,
            excludeId: nil,
            fromId: fromIdForReply,
            createTime: createTimeForReply
        ) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoading = false
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        let items = response.data.list
                        var globalMap: [String:String] = [:]
                        if let anyMap = response.data.mentionedUser {
                            if let dict = anyMap as? [String:String] { globalMap = dict }
                            else if let dict = anyMap as? [String:Any] { for (k,v) in dict { if let name = v as? String { globalMap[k] = name } } }
                        }
                        let replyModels = items.map { self.convertToCommentModel($0, level: 1, mentionMapping: globalMap) }
                        // 在扁平化数组中插入 replyModels，确保追加到已有二级评论之后
                        var insertIndex = indexPath.row + 1
                        while insertIndex < self.comments.count && self.comments[insertIndex].level == 1 {
                            insertIndex += 1
                        }
                        self.comments.insert(contentsOf: replyModels, at: insertIndex)

                        // 计算总共已加载的回复数量
                        let newLoadedCount = loadedCount + replyModels.count
                        
                        // 更新游标：记录最新一条回复的 id / createTime 和已加载数量
                        if let last = items.last {
                            self.replyCursorTracker[parentComment.id] = (last.id, last.createTime, newLoadedCount)
                        } else {
                            // 若本次已无数据，则不再显示展开按钮
                            self.replyCursorTracker[parentComment.id] = (fromIdForReply, createTimeForReply, newLoadedCount)
                        }
                        
                        // 更新 parentComment 展开按钮状态：比较已加载数量与总数
                        parentComment.showExpandReplies = newLoadedCount < parentComment.childCount

                        // 重新计算行高，确保布局正确
                        parentComment.cellHeight = self.calculateCellHeight(
                            text: parentComment.content,
                            hasImage: (parentComment.imageUrl != nil && !(parentComment.imageUrl!.isEmpty)),
                            level: parentComment.level,
                            showExpandReplies: parentComment.showExpandReplies
                        )
                        self.comments[indexPath.row] = parentComment
                        
                        print("[CommentVC] 二级评论加载进度: \(newLoadedCount)/\(parentComment.childCount)")
                        // 直接刷新整表，避免行映射错位引发崩溃
                        self.tableView.reloadData()
                    } else {
                        self.showToast("加载回复失败，请稍后重试")
                    }
                case .failure:
                    self.showToast("加载回复失败，请检查网络")
                }
            }
        }
    }

    // MARK: - Expand Button Handler
    private func handleExpandReplies(forParentIndex parentIdx: Int) {
        // 找到 Comment 数组中的父评论 IndexPath
        let indexPath = IndexPath(row: parentIdx, section: 0)
        handleExpandReplies(at: indexPath)
    }

    // timestamp(from:) 已不再使用，若后续需要可重新实现

    // MARK: - 图片选择与预览
    @objc private func imageButtonTapped() {
        var config = PHPickerConfiguration(photoLibrary: .shared())
        config.filter = .images
        config.selectionLimit = 1
        let picker = PHPickerViewController(configuration: config)
        picker.delegate = self
        present(picker, animated: true)
    }

    // PHPicker 回调
    func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
        picker.dismiss(animated: true)
        guard let itemProvider = results.first?.itemProvider else { return }
        if itemProvider.canLoadObject(ofClass: UIImage.self) {
            itemProvider.loadObject(ofClass: UIImage.self) { [weak self] image, error in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    if let uiImage = image as? UIImage {
                        self.selectedImage = uiImage
                        self.imagePreviewImageView?.image = uiImage
                        self.imagePreviewContainer?.isHidden = false
                        self.imagePreviewCloseButton?.isHidden = false
                        // 调整输入框高度
                        self.commentInputBarHeightConstraint?.constant = 132 + self.previewExtraHeight + (self.inputPanelState == .none ? WindowUtil.safeAreaBottom : 0)
                        UIView.animate(withDuration: 0.25) { self.view.layoutIfNeeded() }
                        // 更新发送按钮状态
                        self.updateSendButtonState()
                    }
                }
            }
        }
    }

    // 关闭图片预览
    @objc private func removeImagePreview() {
        selectedImage = nil
        imagePreviewImageView?.image = nil
        imagePreviewContainer?.isHidden = true
        imagePreviewCloseButton?.isHidden = true
        // 恢复输入框高度
        commentInputBarHeightConstraint?.constant = 132 + (inputPanelState == .none ? WindowUtil.safeAreaBottom : 0)
        UIView.animate(withDuration: 0.25) { self.view.layoutIfNeeded() }
        // 更新发送按钮状态
        updateSendButtonState()
    }

    // MARK: - @ 按钮事件
    @objc private func atButtonTapped() {
        guard let inputField = inputFieldRef else { return }
        print("[VideoComment] @按钮被点击")

        // 确保输入框是第一响应者
        if !inputField.isFirstResponder {
            inputField.becomeFirstResponder()
        }

        // 在光标位置插入"@"并开始监听
        let cursor = inputField.selectedRange.location
        inputField.textStorage.replaceCharacters(in: inputField.selectedRange, with: "@")
        inputField.selectedRange = NSRange(location: cursor + 1, length: 0)

        // 开始@监听模式
        startAtListening(at: cursor)

        // 请求推荐用户（@+空白）
        requestAtUsers(keyword: "", isRecommend: true)
    }

    // MARK: - @监听状态管理
    private func startAtListening(at location: Int) {
        print("[VideoComment] 开始@监听，位置: \(location)")
        isAtListening = true
        isMentioning = true
        mentionStartLocation = location
        mentionKeyword = ""
        mentionPage = 0

        // 保持当前键盘状态，不强制切换
        if inputPanelState != .keyboard {
            inputPanelState = .keyboard
        }
        inputFieldRef?.placeholder = mentionPlaceholder
    }

    private func stopAtListening() {
        isAtListening = false
        isMentioning = false
        isAtPanelVisible = false
        hideAtPanel()

        // 恢复占位文字
        if let name = replyTargetUsername, !name.isEmpty {
            inputFieldRef?.placeholder = "回复 @\(name)"
        } else {
            inputFieldRef?.placeholder = defaultPlaceholder
        }
    }

    // MARK: - 显示/隐藏提及用户面板
    private func showAtPanel() {
        // 创建@面板上方的蒙版（如果不存在）
        if atPanelMaskView == nil {
            let maskView = UIView()
            maskView.backgroundColor = UIColor.clear // 透明蒙版
            view.addSubview(maskView) // 添加到view，确保在输入框蒙版之上
            maskView.translatesAutoresizingMaskIntoConstraints = false

            // 蒙版覆盖评论列表区域，到@面板顶部，动态跟随@面板位置
            atPanelMaskBottomConstraint = maskView.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -(lastKeyboardHeight + 132 + 104))

            NSLayoutConstraint.activate([
                maskView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 60), // 从containerView顶部开始
                maskView.leftAnchor.constraint(equalTo: view.leftAnchor),
                maskView.rightAnchor.constraint(equalTo: view.rightAnchor),
                atPanelMaskBottomConstraint!
            ])

            // 添加点击手势
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(atPanelMaskTapped))
            tapGesture.delegate = self
            maskView.addGestureRecognizer(tapGesture)
            maskView.isUserInteractionEnabled = true

            atPanelMaskView = maskView
            print("[VideoComment] @面板蒙版创建完成，透明蒙版添加到view层级")
        }

        if atPanelView == nil {
            let panel = UIView()
            panel.backgroundColor = .white
            view.addSubview(panel) // 添加到view，确保在@面板蒙版之上
            panel.translatesAutoresizingMaskIntoConstraints = false
            // @面板跟随真实输入框位置，始终在输入框上方
            atPanelBottomConstraint = panel.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -lastKeyboardHeight - 132) // 输入框高度132

            NSLayoutConstraint.activate([
                panel.leftAnchor.constraint(equalTo: view.leftAnchor),
                panel.rightAnchor.constraint(equalTo: view.rightAnchor),
                atPanelBottomConstraint!,
                panel.heightAnchor.constraint(equalToConstant: 104) // 增加高度以适应更大的Cell
            ])
            print("[VideoComment] @面板创建完成，跟随真实输入框位置")

            // 不添加@面板手势，避免干扰CollectionView交互
            // 通过背景手势代理来防止穿透
            panel.isUserInteractionEnabled = true

            // collectionView
            let layout = UICollectionViewFlowLayout()
            layout.scrollDirection = .horizontal
            layout.minimumLineSpacing = 12
            layout.sectionInset = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: 8)
            let collection = UICollectionView(frame: .zero, collectionViewLayout: layout)
            collection.backgroundColor = .white
            collection.showsHorizontalScrollIndicator = false
            collection.dataSource = self
            collection.delegate = self
            collection.register(MentionUserCell.self, forCellWithReuseIdentifier: "MentionUserCell")
            panel.addSubview(collection)
            collection.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                collection.topAnchor.constraint(equalTo: panel.topAnchor),
                collection.bottomAnchor.constraint(equalTo: panel.bottomAnchor),
                collection.leftAnchor.constraint(equalTo: panel.leftAnchor),
                collection.rightAnchor.constraint(equalTo: panel.rightAnchor)
            ])

            atPanelView = panel
            mentionCollectionView = collection
        }

        // 显示@面板和上方蒙版
        atPanelView?.isHidden = false
        atPanelMaskView?.isHidden = false

        // 确保正确的层级顺序
        if let panel = atPanelView, let mask = atPanelMaskView {
            // 使用统一的层级管理方法
            ensureInputViewsAboveMask()

            // 确保@面板相关视图在最上层
            view.bringSubviewToFront(mask)   // @面板蒙版（蓝色）
            view.bringSubviewToFront(panel)  // @面板（最上层）

            // 确保CollectionView在@面板的最上层
            if let collectionView = mentionCollectionView {
                panel.bringSubviewToFront(collectionView)
            }
        }

        inputFieldRef?.placeholder = mentionPlaceholder

        // 强化@面板的键盘保护
        setupAtPanelKeyboardProtection()

        print("[VideoComment] @面板显示完成，CollectionView用户数量: \(mentionUsersData.count)")
    }

    private func hideAtPanel() {
        atPanelView?.isHidden = true
        atPanelMaskView?.isHidden = true // 同时隐藏上方蒙版

        // 恢复占位文字
        if let name = replyTargetUsername, !name.isEmpty {
            inputFieldRef?.placeholder = "回复 @\(name)"
        } else {
            inputFieldRef?.placeholder = defaultPlaceholder
        }

        // 重置输入属性为默认样式
        inputFieldRef?.typingAttributes = [
            .font: UIFont.systemFont(ofSize: 14),
            .foregroundColor: UIColor.black
        ]

        // 清理@相关状态
        mentionUsersData.removeAll()
        mentionCollectionView?.reloadData()
    }

    // MARK: - 统一的@面板状态管理
    private func resetAtPanelState() {
        if isAtListening || isMentioning {
            stopAtListening()
        }
    }

    // MARK: - @面板键盘保护
    private func setupAtPanelKeyboardProtection() {
        // 简化键盘保护，只确保输入框保持第一响应者状态
        if inputFieldRef?.isFirstResponder == false {
            inputFieldRef?.becomeFirstResponder()
        }

        print("[VideoComment] @面板键盘保护设置完成")
    }

    // MARK: - @用户请求管理
    private func requestAtUsers(keyword: String, isRecommend: Bool) {
        // 如果是推荐请求且有缓存，直接使用缓存
        if isRecommend, let cachedUsers = getCachedRecommendUsers() {
            handleAtSearchResult(cachedUsers, keyword: keyword, isRecommend: true)
            return
        }

        // 取消之前的搜索请求
        atSearchWorkItem?.cancel()

        let workItem = DispatchWorkItem { [weak self] in
            self?.searchMentionUsers(keyword: keyword, page: 0, isRecommend: isRecommend)
        }
        atSearchWorkItem = workItem

        // 推荐请求立即执行，搜索请求防抖
        let delay = isRecommend ? 0.1 : 0.5
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem)
    }

    // 获取缓存的推荐用户
    private func getCachedRecommendUsers() -> [UserSearchResultsItem]? {
        if let cacheTime = recommendCacheTime,
           Date().timeIntervalSince(cacheTime) < 300, // 5分钟缓存
           !cachedRecommendUsers.isEmpty {
            return cachedRecommendUsers
        }
        return nil
    }

    // MARK: - 搜索用户（带防抖）- 保留兼容性
    private func scheduleSearch(keyword: String) {
        requestAtUsers(keyword: keyword, isRecommend: keyword.isEmpty)
    }

    private func searchMentionUsers(keyword: String, page: Int, isRecommend: Bool = false) {
        let requestType = isRecommend ? "推荐" : "搜索"
        print("[VideoComment] \(requestType)@用户 - 关键词: '\(keyword)', 页码: \(page)")

        APIManager.shared.searchUser(keywords: keyword, page: page, size: mentionPageSize, isIgnore: true) { [weak self] result in
            guard let self = self else { return }
            switch result {
            case .success(let data):
                let newList = data.data.list
                self.mentionTotal = data.data.total
                print("[VideoComment] \(requestType)@用户结果 - 总数: \(data.data.total), 当前页数量: \(newList.count)")

                DispatchQueue.main.async {
                    self.handleAtSearchResult(newList, keyword: keyword, isRecommend: isRecommend)
                }

            case .failure(let error):
                print("[VideoComment] \(requestType)@用户失败: \(error)")
                DispatchQueue.main.async {
                    self.handleAtSearchResult([], keyword: keyword, isRecommend: isRecommend)
                }
            }
        }
    }

    // 处理@搜索结果
    private func handleAtSearchResult(_ users: [UserSearchResultsItem], keyword: String, isRecommend: Bool) {
        // 缓存推荐用户
        if isRecommend && !users.isEmpty {
            cachedRecommendUsers = users
            recommendCacheTime = Date()
        }

        if users.isEmpty {
            // 无数据：隐藏面板，但保持监听状态（如果正在监听）
            if isAtListening {
                hideAtPanel()
                isAtPanelVisible = false
                print("[VideoComment] 无@用户数据，隐藏面板但保持监听")
            }
        } else {
            // 有数据：显示面板
            mentionUsersData = users
            mentionHasMore = users.count >= mentionPageSize

            if isAtListening {
                showAtPanel()
                isAtPanelVisible = true
                inputPanelState = .atPanel
                print("[VideoComment] 显示@面板，用户数量: \(users.count)")
            }

            mentionCollectionView?.reloadData()
        }
    }

    // MARK: - 插入选中用户
    private func insertMentionUser(_ user: UserSearchResultsItem) {
        guard let textView = inputFieldRef else { return }
        let cursorPosition = textView.selectedRange.location
        let mentionRange = NSRange(location: mentionStartLocation, length: cursorPosition - mentionStartLocation)

        let mentionDisplay = "@" + user.nickName
        let mentionAttr = NSMutableAttributedString(string: mentionDisplay)
        mentionAttr.addAttribute(.foregroundColor, value: UIColor.systemBlue, range: NSRange(location: 0, length: mentionDisplay.count))
        mentionAttr.addAttribute(NSAttributedString.Key("mentionId"), value: user.customerId, range: NSRange(location: 0, length: mentionDisplay.count))

        let separatorAttr = NSAttributedString(string: "\u{200B}")

        let storage = textView.textStorage
        storage.beginEditing()
        storage.replaceCharacters(in: mentionRange, with: mentionAttr)
        storage.insert(separatorAttr, at: mentionStartLocation + mentionDisplay.count)
        storage.endEditing()

        textView.selectedRange = NSRange(location: mentionStartLocation + mentionDisplay.count + 1, length: 0)

        // 重置输入属性为默认样式，确保后续输入的文本是正常的黑色
        textView.typingAttributes = [
            .font: UIFont.systemFont(ofSize: 14),
            .foregroundColor: UIColor.black
        ]

        // 记录@用户信息
        mentionedUserDict[user.customerId] = user.nickName

        // 停止@监听，用户已选择完成
        stopAtListening()

        // 切换回键盘状态并保持焦点
        inputPanelState = .keyboard
        inputFieldRef?.becomeFirstResponder()

        // 延迟更新发送按钮状态，确保文本变化已完成
        DispatchQueue.main.async {
            self.updateSendButtonState()
        }

        print("[VideoComment] 用户选择完成: @\(user.nickName), 停止监听")
    }

    // MARK: - 处理输入变化
    func textViewDidChange(_ textView: UITextView) {
        let cursor = textView.selectedRange.location
        let text = textView.text ?? ""
        let nsText = text as NSString

        // 更新发送按钮状态
        updateSendButtonState()

        // 处理@监听状态
        if isAtListening {
            // 检查@符号是否还存在
            if mentionStartLocation >= nsText.length || mentionStartLocation < 0 || nsText.character(at: mentionStartLocation) != 64 { // "@"
                // @符号被删除，停止监听
                stopAtListening()
                return
            }

            // 计算@后面的关键词
            let rangeLength = cursor - mentionStartLocation - 1
            if rangeLength >= 0 {
                let range = NSRange(location: mentionStartLocation + 1, length: rangeLength)
                if let swiftRange = Range(range, in: text) {
                    let newKeyword = String(text[swiftRange])

                    // 只有关键词变化时才请求
                    if newKeyword != mentionKeyword {
                        mentionKeyword = newKeyword

                        if newKeyword.isEmpty {
                            // @+空白：请求推荐
                            requestAtUsers(keyword: "", isRecommend: true)
                        } else {
                            // @+关键词：请求搜索
                            requestAtUsers(keyword: newKeyword, isRecommend: false)
                        }
                    }
                }
            }
        } else {
            // 检查是否新输入了@
            if cursor > 0 && cursor - 1 < nsText.length, nsText.character(at: cursor - 1) == 64 { // 新输入@
                startAtListening(at: cursor - 1)
                requestAtUsers(keyword: "", isRecommend: true)
            }
        }
    }

    // MARK: - 更新发送按钮状态
    private func updateSendButtonState() {
        guard let sendButton = sendButtonRef else { return }

        // 修复：使用与发送时相同的逻辑来判断是否有内容
        let rawText = inputFieldRef?.text ?? ""
        let processedText = processContentForSending(from: rawText).trimmingCharacters(in: .whitespacesAndNewlines)

        let hasText = !processedText.isEmpty
        let hasImage = selectedImage != nil
        let canSend = hasText || hasImage

        sendButton.isEnabled = canSend

        if canSend {
            // 有内容时：完全不透明的橙色
            sendButton.backgroundColor = UIColor(hex: "#FF8F1F")
            sendButton.setTitleColor(.white, for: .normal)
        } else {
            // 无内容时：半透明的橙色
            sendButton.backgroundColor = UIColor(hex: "#FF8F1F", alpha: 0.5)
            sendButton.setTitleColor(UIColor.white.withAlphaComponent(0.7), for: .normal)
        }

        print("[VideoComment] 发送按钮状态更新: rawText='\(rawText)', processedText='\(processedText)', canSend=\(canSend)")
    }

    // MARK: - UICollectionView DataSource & Delegate
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return mentionUsersData.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "MentionUserCell", for: indexPath) as! MentionUserCell
        cell.configure(with: mentionUsersData[indexPath.item])
        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let user = mentionUsersData[indexPath.item]
        print("[VideoComment] CollectionView点击用户: \(user.nickName)")
        insertMentionUser(user)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        // 增加Cell尺寸，提供更大的点击范围
        return CGSize(width: 80, height: 76)
    }

    // MARK: - 提及用户 Cell
    class MentionUserCell: UICollectionViewCell {
        private let avatarImageView = UIImageView()
        private let nameLabel = UILabel()
        private let backgroundHighlightView = UIView()

        override init(frame: CGRect) {
            super.init(frame: frame)
            setupUI()
        }

        required init?(coder: NSCoder) {
            super.init(coder: coder)
            setupUI()
        }

        private func setupUI() {
            // 背景高亮视图（用于点击反馈）
            backgroundHighlightView.backgroundColor = UIColor.black.withAlphaComponent(0.1)
            backgroundHighlightView.layer.cornerRadius = 8
            backgroundHighlightView.isHidden = true
            backgroundHighlightView.translatesAutoresizingMaskIntoConstraints = false
            contentView.addSubview(backgroundHighlightView)

            // 头像设置
            avatarImageView.layer.cornerRadius = 24
            avatarImageView.clipsToBounds = true
            avatarImageView.translatesAutoresizingMaskIntoConstraints = false
            contentView.addSubview(avatarImageView)

            // 用户名标签设置
            nameLabel.font = UIFont.systemFont(ofSize: 13)
            nameLabel.textAlignment = .center
            nameLabel.numberOfLines = 1
            nameLabel.translatesAutoresizingMaskIntoConstraints = false
            contentView.addSubview(nameLabel)

            // 约束设置
            NSLayoutConstraint.activate([
                // 背景高亮视图填满整个cell，增加点击范围
                backgroundHighlightView.topAnchor.constraint(equalTo: contentView.topAnchor),
                backgroundHighlightView.leftAnchor.constraint(equalTo: contentView.leftAnchor),
                backgroundHighlightView.rightAnchor.constraint(equalTo: contentView.rightAnchor),
                backgroundHighlightView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),

                // 头像约束
                avatarImageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 4),
                avatarImageView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
                avatarImageView.widthAnchor.constraint(equalToConstant: 48),
                avatarImageView.heightAnchor.constraint(equalToConstant: 48),

                // 用户名约束
                nameLabel.topAnchor.constraint(equalTo: avatarImageView.bottomAnchor, constant: 4),
                nameLabel.leftAnchor.constraint(equalTo: contentView.leftAnchor, constant: 2),
                nameLabel.rightAnchor.constraint(equalTo: contentView.rightAnchor, constant: -2),
                nameLabel.heightAnchor.constraint(equalToConstant: 20)
            ])
        }

        func configure(with user: UserSearchResultsItem) {
            nameLabel.text = user.nickName
            if let url = URL(string: user.wxAvator), !user.wxAvator.isEmpty {
                avatarImageView.kf.setImage(with: url, placeholder: UIImage(named: "avatar_placeholder"))
            } else {
                avatarImageView.image = UIImage(named: "avatar_placeholder")
            }
        }

        // 添加点击反馈效果
        override var isHighlighted: Bool {
            didSet {
                UIView.animate(withDuration: 0.1) {
                    self.backgroundHighlightView.isHidden = !self.isHighlighted
                    self.transform = self.isHighlighted ? CGAffineTransform(scaleX: 0.95, y: 0.95) : .identity
                }
            }
        }
    }

    /// 处理文本，将 @用户名 转为 @id\，并移除零宽空格
    private func processContentForSending(from plain: String) -> String {
        var processed = plain.replacingOccurrences(of: "\u{200B}", with: "")
        // 按用户名长度降序，避免子串冲突
        let sorted = mentionedUserDict.sorted { $0.value.count > $1.value.count }
        for (id, name) in sorted {
            let pattern = "@" + NSRegularExpression.escapedPattern(for: name)
            processed = processed.replacingOccurrences(of: pattern, with: "@" + id + "\\")
        }
        return processed
    }

    // MARK: - 点赞 / 不喜欢 操作
    private func toggleLike(forRow mapRow: Int) {
        let item = cachedRowMap[mapRow]
        let idx = item.1
        guard idx < comments.count, let commentId = Int(comments[idx].id) else { return }
        var model = comments[idx]
        let newLikeState = !model.isLiked
        let operateValue = newLikeState ? 1 : 2

        // 先进行本地更新，提供即时反馈
        model.isLiked = newLikeState
        if newLikeState {
            model.likes += 1
            // 点赞时，如果之前是不喜欢状态，需要取消不喜欢
            if model.isDisliked {
                model.isDisliked = false
                model.dislikes = max(model.dislikes - 1, 0)
            }
        } else {
            model.likes = max(model.likes - 1, 0)
        }
        comments[idx] = model
        tableView.reloadRows(at: [IndexPath(row: mapRow, section: 0)], with: .none)

        // 执行点赞操作
        APIManager.shared.operateWorkComment(commentId: commentId, operateType: 1, operateValue: operateValue, worksId: videoId) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(_):
                    // 点赞成功后，如果是执行点赞操作，需要额外执行不喜欢+取消来确保互斥
                    if newLikeState {
                        self?.executeDislikeCancel(commentId: commentId, idx: idx, mapRow: mapRow)
                    }
                case .failure(_):
                    // 失败回滚
                    self?.refreshCommentRow(idx: idx, mapRow: mapRow)
                }
            }
        }
    }

    private func toggleDislike(forRow mapRow: Int) {
        let item = cachedRowMap[mapRow]
        let idx = item.1
        guard idx < comments.count, let commentId = Int(comments[idx].id) else { return }
        var model = comments[idx]
        let newDislikeState = !model.isDisliked
        let operateValue = newDislikeState ? 1 : 2

        // 先进行本地更新，提供即时反馈
        model.isDisliked = newDislikeState
        if newDislikeState {
            model.dislikes += 1
            // 不喜欢时，如果之前是点赞状态，需要取消点赞
            if model.isLiked {
                model.isLiked = false
                model.likes = max(model.likes - 1, 0)
            }
        } else {
            model.dislikes = max(model.dislikes - 1, 0)
        }
        comments[idx] = model
        tableView.reloadRows(at: [IndexPath(row: mapRow, section: 0)], with: .none)

        // 不喜欢操作需要同时执行两个API调用来确保互斥
        if newDislikeState {
            // 执行不喜欢+执行 和 点赞+取消
            executeDislikeAndCancelLike(commentId: commentId, idx: idx, mapRow: mapRow)
        } else {
            // 只执行不喜欢+取消
            APIManager.shared.operateWorkComment(commentId: commentId, operateType: 2, operateValue: operateValue, worksId: videoId) { [weak self] result in
                if case .failure(_) = result {
                    DispatchQueue.main.async { self?.refreshCommentRow(idx: idx, mapRow: mapRow) }
                }
            }
        }
    }

    private func refreshCommentRow(idx: Int, mapRow: Int) {
        guard idx < comments.count else { return }
        // 重新拉取评论数据或简单刷新状态（此处简单刷新）
        tableView.reloadRows(at: [IndexPath(row: mapRow, section: 0)], with: .none)
    }

    // MARK: - 互斥操作辅助方法

    /// 执行不喜欢+取消操作（点赞成功后调用，确保互斥）
    private func executeDislikeCancel(commentId: Int, idx: Int, mapRow: Int) {
        APIManager.shared.operateWorkComment(commentId: commentId, operateType: 2, operateValue: 2, worksId: videoId) { [weak self] result in
            // 这个操作主要是为了确保服务端状态正确，UI已经在点赞时更新了
            if case .failure(_) = result {
                // 如果取消不喜欢失败，可能需要重新获取状态，但通常可以忽略
                print("取消不喜欢操作失败，但不影响用户体验")
            }
        }
    }

    /// 执行不喜欢+执行 和 点赞+取消操作（不喜欢按钮点击时调用）
    private func executeDislikeAndCancelLike(commentId: Int, idx: Int, mapRow: Int) {
        let group = DispatchGroup()
        var hasError = false

        // 执行不喜欢+执行
        group.enter()
        APIManager.shared.operateWorkComment(commentId: commentId, operateType: 2, operateValue: 1, worksId: videoId) { result in
            if case .failure(_) = result {
                hasError = true
            }
            group.leave()
        }

        // 执行点赞+取消
        group.enter()
        APIManager.shared.operateWorkComment(commentId: commentId, operateType: 1, operateValue: 2, worksId: videoId) { result in
            if case .failure(_) = result {
                hasError = true
            }
            group.leave()
        }

        // 等待两个操作完成
        group.notify(queue: .main) { [weak self] in
            if hasError {
                // 如果有任何操作失败，回滚UI状态
                self?.refreshCommentRow(idx: idx, mapRow: mapRow)
            }
        }
    }

    // MARK: - 导航到个人主页
    private func navigateToPersonalHomepage(userId: String) {
        let personalVC = PersonalHomepageViewController()
        personalVC.userId = userId
        if let nav = self.navigationController {
            nav.pushViewController(personalVC, animated: true)
        } else {
            let navVC = UINavigationController(rootViewController: personalVC)
            navVC.modalPresentationStyle = .fullScreen
            self.present(navVC, animated: true)
        }
    }

    // 新增：获取当前用户ID方法（请根据实际用户体系实现）
    private func getCurrentUserId() -> String {
        // TODO: 替换为实际获取当前登录用户ID的逻辑
        return "当前用户ID"
    }

    // 新增：处理评论操作弹窗回调
    private func handleCommentAction(_ action: CommentActionType, for model: CommentModel, at indexPath: IndexPath) {
        switch action {
        case .reply:
            // 触发回复逻辑（与didSelectRowAt一致）
            let realIndex = indexPath.row
            clickedIndex = indexPath.row
            var parentIndex = realIndex
            if model.level == 1 {
                var idx = realIndex
                while idx >= 0 && idx < comments.count {
                    if comments[idx].level == 0 { parentIndex = idx; break }
                    idx -= 1
                }
            }
            selectedParentIndex = parentIndex
            if model.level == 0 {
                replyTargetUsername = nil
                replyPid = Int(model.id)
                replyPcustomerId = nil
            } else {
                replyTargetUsername = model.username
                replyPid = Int(comments[parentIndex].id)
                replyPcustomerId = model.customerId
            }
            inputPanelState = .keyboard
        case .copy:
            UIPasteboard.general.string = model.content
            showToast("已复制")
        case .report:
            // 简化举报流程：直接本地 HUD 提示
            showToast("举报成功")
        case .delete:
            // 调用删除接口并在成功后本地删除评论
            confirmDeleteComment(model: model, mapRow: indexPath.row)
        }
    }

    // MARK: - 删除评论
    /// 弹出确认框后删除评论
    private func confirmDeleteComment(model: CommentModel, mapRow: Int) {
        guard let commentId = Int(model.id) else { return }

        let alert = UIAlertController(title: "删除评论", message: "确定要删除这条评论吗？", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "删除", style: .destructive, handler: { [weak self] _ in
            self?.requestDeleteComment(commentId: commentId, model: model, mapRow: mapRow)
        }))
        present(alert, animated: true)
    }

    /// 发起接口请求删除评论
    private func requestDeleteComment(commentId: Int, model: CommentModel, mapRow: Int) {
        APIManager.shared.deleteComment(commentId: commentId) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let resp):
                    if resp.status == 200 {
                        self.applyLocalDelete(for: model, mapRow: mapRow)
                        self.showToast("已删除")
                    } else {
                        self.showToast(resp.displayMessage.isEmpty ? "删除失败" : resp.displayMessage)
                    }
                case .failure(let error):
                    self.showToast("删除失败：\(error.localizedDescription)")
                }
            }
        }
    }

    /// 根据评论层级删除本地数据并更新 UI
    private func applyLocalDelete(for model: CommentModel, mapRow: Int) {
        let idxInComments = cachedRowMap[mapRow].1
        guard idxInComments < comments.count else { return }

        var indicesToRemove: [Int] = [idxInComments]

        if model.level == 0 {
            // 一级评论：同时删除其所有二级回复
            var next = idxInComments + 1
            while next < comments.count && comments[next].level == 1 {
                indicesToRemove.append(next)
                next += 1
            }
            // 更新评论总数
            commentCount = max(commentCount - 1, 0)
            titleLabel.text = "评论 \(commentCount)"
        } else {
            // 二级评论：需要更新父评论的 childCount
            var parentIdx = idxInComments - 1
            while parentIdx >= 0 && parentIdx < comments.count && comments[parentIdx].level != 0 { parentIdx -= 1 }
            if parentIdx >= 0 {
                var parent = comments[parentIdx]
                parent.childCount = max(parent.childCount - 1, 0)
                // 重新计算父评论高度，防止布局问题
                parent.cellHeight = calculateCellHeight(text: parent.content, hasImage: (parent.imageUrl != nil && !(parent.imageUrl!.isEmpty)), level: parent.level, showExpandReplies: parent.showExpandReplies)
                comments[parentIdx] = parent
            }
        }

        // 先根据当前 rowMap 记录需要删除的行
        let rowsToDelete = cachedRowMap.enumerated().filter { indicesToRemove.contains($0.element.1) }.map { IndexPath(row: $0.offset, section: 0) }

        // 按降序删除数据，避免索引错位
        for i in indicesToRemove.sorted(by: >) {
            comments.remove(at: i)
        }

        // 更新游标追踪器，防止展开时崩溃
        if model.level == 0 {
            replyCursorTracker.removeValue(forKey: model.id)
        }

        tableView.beginUpdates()
        tableView.deleteRows(at: rowsToDelete, with: .automatic)
        // 如果涉及父评论信息变化，刷新该行
        if model.level == 1 {
            // 重新计算行映射
            let newMap = cachedRowMap
            // 找到父评论在 comments 中的索引
            var search = idxInComments - 1
            var parentId: String? = nil
            while search >= 0 {
                if comments[search].level == 0 { parentId = comments[search].id; break }
                search -= 1
            }
            if let pid = parentId, let parentMapRow = newMap.firstIndex(where: { $0.0 == .comment && comments[$0.1].id == pid }) {
                tableView.reloadRows(at: [IndexPath(row: parentMapRow, section: 0)], with: .none)
            }
        }
        tableView.endUpdates()
    }
}
